<template>
  <g class="connection-line" :class="{ 'selected': isSelected, 'overlapping': hasOverlapping }" @click="selectLine">
    <!-- 选中状态的高亮边框 -->
    <path
      v-if="isSelected"
      :d="pathData"
      stroke="#409eff"
      :stroke-width="connection.style.strokeWidth + 8"
      fill="none"
      opacity="0.3"
      style="pointer-events: none;"
    />

    <!-- 管道样式连接线 - 外层边框 -->
    <path
      :d="pathData"
      :stroke="isSelected ? '#409eff' : '#f0f0f0'"
      :stroke-width="connection.style.strokeWidth + 4"
      fill="none"
      style="pointer-events: stroke; cursor: pointer;"
    />

    <!-- 内层连接线 -->
    <path
      :d="pathData"
      :stroke="connection.style.strokeColor"
      :stroke-width="connection.style.strokeWidth"
      :stroke-dasharray="connection.style.strokeDasharray"
      fill="none"
      :class="{ 'animated': connection.animation?.enabled }"
      style="pointer-events: stroke; cursor: pointer;"
    />

    <!-- 箭头定义 -->
    <defs>
      <marker
        :id="`arrow-${connection.id}`"
        viewBox="0 0 12 8"
        refX="6"
        refY="4"
        :markerWidth="connection.style.arrowSize"
        :markerHeight="connection.style.arrowSize"
        orient="auto"
        markerUnits="strokeWidth"
      >
        <path
          d="M0,0 L0,8 L12,4 z"
          :fill="connection.style.arrowColor"
        />
      </marker>
    </defs>

    <!-- 沿路径分布的箭头 -->
    <g v-if="arrowPositions.length > 0">
      <polygon
        v-for="(pos, index) in arrowPositions"
        :key="index"
        :points="getArrowPoints(pos)"
        :fill="connection.style.arrowColor"
        style="pointer-events: none;"
      />
    </g>

    <!-- 连接点编辑手柄（选中时显示） -->
    <g v-if="isSelected && pathPoints.length > 2">
      <circle
        v-for="(point, index) in editablePoints"
        :key="`handle-${index}`"
        :cx="point.x"
        :cy="point.y"
        r="4"
        fill="#409eff"
        stroke="#fff"
        stroke-width="2"
        style="cursor: move; pointer-events: all;"
        @mousedown.stop="startDragHandle($event, index)"
      />
    </g>
    
    <!-- 动画效果 -->
    <template v-if="connection.animation?.enabled">
      <!-- 流动动画 -->
      <circle
        v-if="connection.animation.type === 'flow'"
        r="3"
        :fill="connection.style.strokeColor"
        opacity="0.8"
      >
        <animateMotion
          :dur="`${connection.animation.speed}s`"
          repeatCount="indefinite"
          :path="pathData"
        />
      </circle>
      
      <!-- 脉冲动画 -->
      <path
        v-if="connection.animation.type === 'pulse'"
        :d="pathData"
        :stroke="connection.style.strokeColor"
        :stroke-width="connection.style.strokeWidth + 2"
        fill="none"
        opacity="0.5"
      >
        <animate
          attributeName="opacity"
          values="0.5;1;0.5"
          :dur="`${connection.animation.speed}s`"
          repeatCount="indefinite"
        />
      </path>
    </template>
  </g>
</template>

<script setup lang="ts">
import { computed, ref, watch, nextTick } from 'vue'
import type { ComponentConnection, ConfigurationComponent } from '../types'

// 性能优化：路径计算缓存
const pathCache = new Map<string, string>()
const pointsCache = new Map<string, Array<{ x: number; y: number }>>()

// 新增：重合检测缓存
const overlapCache = new Map<string, boolean>()
const OVERLAP_CACHE_SIZE = 100

// 新增：空间分割网格，用于快速筛选连接线
const GRID_SIZE = 200 // 网格大小
const getGridKey = (x: number, y: number) => `${Math.floor(x / GRID_SIZE)},${Math.floor(y / GRID_SIZE)}`

// 防抖优化：临时连接线更新频率限制
let tempUpdateTimer: number | null = null
const tempPathData = ref('')

const props = defineProps<{
  connection: ComponentConnection
  sourceComponent: ConfigurationComponent | null
  targetComponent?: ConfigurationComponent | null
  tempEndPoint?: { x: number; y: number } | null
  isSelected?: boolean
  allComponents?: ConfigurationComponent[]
  allConnections?: ComponentConnection[]
  connectionIndex?: number
}>()

const emit = defineEmits(['select', 'delete', 'update'])

// 优化后的重合检测（使用空间分割和缓存）
const hasOverlapping = computed(() => {
  if (!props.allConnections || !props.connectionIndex || props.allConnections.length < 2) return false

  const currentConnection = props.connection
  const cacheKey = generateOverlapCacheKey(currentConnection)
  
  // 检查缓存
  if (overlapCache.has(cacheKey)) {
    return overlapCache.get(cacheKey)!
  }

  let result = false
  
  // 获取当前连接线的包围盒，用于快速筛选
  const currentBounds = getConnectionBounds(currentConnection)
  if (!currentBounds) {
    overlapCache.set(cacheKey, false)
    return false
  }

  // 只检查可能重叠的连接线（空间筛选）
  const candidateConnections = getCandidateConnections(currentBounds, props.allConnections, props.connectionIndex!)
  
  // 详细重合检测（只对候选连接线进行）
  for (const otherConnection of candidateConnections) {
    if (isConnectionOverlapping(currentConnection, otherConnection)) {
      result = true
      break
    }
  }

  // 缓存结果
  if (overlapCache.size > OVERLAP_CACHE_SIZE) {
    const firstKey = overlapCache.keys().next().value
    overlapCache.delete(firstKey)
  }
  overlapCache.set(cacheKey, result)

  return result
})

// 生成重合检测缓存键
const generateOverlapCacheKey = (connection: ComponentConnection) => {
  if (!props.sourceComponent) return ''
  
  const sourcePos = `${props.sourceComponent.x}-${props.sourceComponent.y}`
  const targetPos = props.targetComponent ? `${props.targetComponent.x}-${props.targetComponent.y}` : 'temp'
  return `${connection.id}-${sourcePos}-${targetPos}-${connection.sourceAnchor.position}-${connection.targetAnchor.position}`
}

// 获取连接线的包围盒
const getConnectionBounds = (connection: ComponentConnection) => {
  if (!props.sourceComponent) return null
  
  const sourcePoint = getAnchorPoint(props.sourceComponent, connection.sourceAnchor.position)
  let targetPoint: { x: number; y: number }
  
  if (props.targetComponent) {
    targetPoint = getAnchorPoint(props.targetComponent, connection.targetAnchor.position)
  } else if (props.tempEndPoint) {
    targetPoint = props.tempEndPoint
  } else {
    return null
  }
  
  return {
    minX: Math.min(sourcePoint.x, targetPoint.x) - 10,
    maxX: Math.max(sourcePoint.x, targetPoint.x) + 10,
    minY: Math.min(sourcePoint.y, targetPoint.y) - 10,
    maxY: Math.max(sourcePoint.y, targetPoint.y) + 10
  }
}

// 获取候选连接线（空间筛选）
const getCandidateConnections = (bounds: any, allConnections: ComponentConnection[], currentIndex: number) => {
  const candidates: ComponentConnection[] = []
  
  for (let i = 0; i < allConnections.length; i++) {
    if (i === currentIndex) continue
    
    const otherConnection = allConnections[i]
    const otherBounds = getConnectionBoundsForConnection(otherConnection)
    
    if (otherBounds && boundsIntersect(bounds, otherBounds)) {
      candidates.push(otherConnection)
    }
  }
  
  return candidates
}

// 获取指定连接线的包围盒
const getConnectionBoundsForConnection = (connection: ComponentConnection) => {
  const sourceComp = props.allComponents?.find(c => c.id === connection.sourceComponent)
  const targetComp = props.allComponents?.find(c => c.id === connection.targetComponent)
  
  if (!sourceComp || !targetComp) return null
  
  const sourcePoint = getAnchorPoint(sourceComp, connection.sourceAnchor.position)
  const targetPoint = getAnchorPoint(targetComp, connection.targetAnchor.position)
  
  return {
    minX: Math.min(sourcePoint.x, targetPoint.x) - 10,
    maxX: Math.max(sourcePoint.x, targetPoint.x) + 10,
    minY: Math.min(sourcePoint.y, targetPoint.y) - 10,
    maxY: Math.max(sourcePoint.y, targetPoint.y) + 10
  }
}

// 判断两个包围盒是否相交
const boundsIntersect = (bounds1: any, bounds2: any) => {
  return !(bounds1.maxX < bounds2.minX || 
          bounds2.maxX < bounds1.minX || 
          bounds1.maxY < bounds2.minY || 
          bounds2.maxY < bounds1.minY)
}

// 生成缓存键
const generateCacheKey = () => {
  if (!props.sourceComponent) return ''

  const sourceKey = `${props.sourceComponent.id}-${props.sourceComponent.x}-${props.sourceComponent.y}-${props.connection.sourceAnchor.position}`

  if (props.tempEndPoint) {
    return `${sourceKey}-temp-${Math.round(props.tempEndPoint.x)}-${Math.round(props.tempEndPoint.y)}`
  } else if (props.targetComponent) {
    const targetKey = `${props.targetComponent.id}-${props.targetComponent.x}-${props.targetComponent.y}-${props.connection.targetAnchor.position}`
    return `${sourceKey}-${targetKey}`
  }

  return sourceKey
}

// 计算路径点（带缓存优化）
const pathPoints = computed(() => {
  if (!props.sourceComponent) return []

  // 生成缓存键
  const cacheKey = generateCacheKey()

  // 检查缓存
  if (pointsCache.has(cacheKey)) {
    return pointsCache.get(cacheKey)!
  }

  // 使用锚点边缘位置
  const sourcePoint = getAnchorPoint(props.sourceComponent, props.connection.sourceAnchor.position)

  let targetPoint: { x: number; y: number }

  if (props.tempEndPoint) {
    // 临时连接线，目标点是鼠标位置（不缓存临时连接线）
    targetPoint = props.tempEndPoint

    // 如果连接有自定义路径点，使用自定义路径
    if (props.connection.pathPoints && props.connection.pathPoints.length > 0) {
      return [sourcePoint, ...props.connection.pathPoints, targetPoint]
    }

    // 生成智能路径（临时连接线使用简化算法）
    return generateSimplePath(sourcePoint, targetPoint, props.connection.sourceAnchor.position, props.connection.targetAnchor.position)
  } else if (props.targetComponent) {
    // 正式连接线，使用目标组件的锚点边缘位置
    targetPoint = getAnchorPoint(props.targetComponent, props.connection.targetAnchor.position)
  } else {
    return [sourcePoint]
  }

  let result: Array<{ x: number; y: number }>

  // 如果连接有自定义路径点，使用自定义路径
  if (props.connection.pathPoints && props.connection.pathPoints.length > 0) {
    result = [sourcePoint, ...props.connection.pathPoints, targetPoint]
  } else {
    // 否则生成智能路径
    result = generateSmartPath(sourcePoint, targetPoint, props.connection.sourceAnchor.position, props.connection.targetAnchor.position)
  }

  // 缓存结果（只缓存正式连接线）
  pointsCache.set(cacheKey, result)

  return result
})

// 生成简化路径（用于临时连接线，性能优化）
const generateSimplePath = (
  start: { x: number; y: number },
  end: { x: number; y: number },
  startAnchor: string,
  endAnchor: string
) => {
  const points = [start]
  const minDistance = 40

  // 简单延伸
  const firstPoint = getExtendedPoint(start, startAnchor, minDistance)
  points.push(firstPoint)

  // 简单L型连接，不进行复杂的避障计算
  if (needsIntermediatePoints(firstPoint, end, startAnchor, endAnchor)) {
    const intermediatePoints = generateIntermediatePoints(firstPoint, end, startAnchor, endAnchor)
    points.push(...intermediatePoints)
  }

  points.push(end)
  return points
}

// 计算路径数据（带缓存优化）
const pathData = computed(() => {
  if (pathPoints.value.length < 2) return ''

  const points = pathPoints.value

  // 生成路径缓存键
  const pathCacheKey = points.map(p => `${Math.round(p.x)},${Math.round(p.y)}`).join('-')

  // 检查缓存
  if (pathCache.has(pathCacheKey)) {
    return pathCache.get(pathCacheKey)!
  }

  let result: string

  // 如果只有两个点，使用直线
  if (points.length === 2) {
    result = `M ${points[0].x} ${points[0].y} L ${points[1].x} ${points[1].y}`
  } else {
    // 多个点时使用圆滑的路径
    result = generateSmoothPath(points)
  }

  // 缓存结果
  pathCache.set(pathCacheKey, result)

  return result
})

// 监听组件变化，清理相关缓存
watch(() => [props.sourceComponent, props.targetComponent, props.connection], () => {
  // 清理缓存以确保数据一致性
  const cacheKey = generateCacheKey()
  pointsCache.delete(cacheKey)

  // 限制缓存大小，防止内存泄漏
  if (pathCache.size > 100) {
    const firstKey = pathCache.keys().next().value
    pathCache.delete(firstKey)
  }
  if (pointsCache.size > 50) {
    const firstKey = pointsCache.keys().next().value
    pointsCache.delete(firstKey)
  }
}, { deep: true })

// 生成圆滑的路径（性能优化版本）
const generateSmoothPath = (points: Array<{ x: number; y: number }>) => {
  if (points.length < 2) return ''

  // 预分配字符串数组，减少字符串拼接开销
  const pathSegments: string[] = [`M ${points[0].x} ${points[0].y}`]
  const cornerRadius = 15 // 圆角半径

  for (let i = 1; i < points.length; i++) {
    const current = points[i]
    const previous = points[i - 1]
    const next = points[i + 1]

    if (!next) {
      // 最后一个点，直接连接
      pathSegments.push(`L ${current.x} ${current.y}`)
    } else {
      // 中间点，添加圆角
      const smoothPoints = calculateSmoothCorner(previous, current, next, cornerRadius)

      if (smoothPoints) {
        // 连接到圆角起点
        pathSegments.push(`L ${smoothPoints.start.x} ${smoothPoints.start.y}`)
        // 添加圆角弧线
        pathSegments.push(`Q ${current.x} ${current.y} ${smoothPoints.end.x} ${smoothPoints.end.y}`)
      } else {
        // 无法创建圆角，使用直线
        pathSegments.push(`L ${current.x} ${current.y}`)
      }
    }
  }

  return pathSegments.join(' ')
}

// 计算圆滑转角的控制点（性能优化版本）
const calculateSmoothCorner = (
  prev: { x: number; y: number },
  current: { x: number; y: number },
  next: { x: number; y: number },
  radius: number
) => {
  // 计算两个向量
  const vec1x = current.x - prev.x
  const vec1y = current.y - prev.y
  const vec2x = next.x - current.x
  const vec2y = next.y - current.y

  // 计算向量长度（避免重复计算）
  const len1Sq = vec1x * vec1x + vec1y * vec1y
  const len2Sq = vec2x * vec2x + vec2y * vec2y

  if (len1Sq === 0 || len2Sq === 0) return null

  const len1 = Math.sqrt(len1Sq)
  const len2 = Math.sqrt(len2Sq)

  // 标准化向量
  const unit1x = vec1x / len1
  const unit1y = vec1y / len1
  const unit2x = vec2x / len2
  const unit2y = vec2y / len2

  // 计算可用的最大半径
  const maxRadius = Math.min(len1 * 0.5, len2 * 0.5, radius)

  if (maxRadius <= 0) return null

  // 计算圆角的起点和终点
  return {
    start: {
      x: current.x - unit1x * maxRadius,
      y: current.y - unit1y * maxRadius
    },
    end: {
      x: current.x + unit2x * maxRadius,
      y: current.y + unit2y * maxRadius
    }
  }
}

// 可编辑的路径点（排除起点和终点）
const editablePoints = computed(() => {
  return pathPoints.value.slice(1, -1)
})

// 生成智能路径（避障算法）
const generateSmartPath = (
  start: { x: number; y: number },
  end: { x: number; y: number },
  startAnchor: string,
  endAnchor: string
) => {
  // 获取所有组件的边界框，用于避障
  const obstacles = getAllComponentBounds()

  // 使用简化的避障算法
  return generateSimpleAvoidancePath(start, end, startAnchor, endAnchor, obstacles)
}

// 简化的避障路径生成
const generateSimpleAvoidancePath = (
  start: { x: number; y: number },
  end: { x: number; y: number },
  startAnchor: string,
  endAnchor: string,
  obstacles: Array<{x: number, y: number, width: number, height: number, id: string}>
) => {
  const points = [start]
  const minDistance = 40

  // 从起点延伸一个点
  const firstPoint = getExtendedPoint(start, startAnchor, minDistance)
  points.push(firstPoint)

  // 计算到终点前的延伸点（目标锚点向外延伸）
  const lastPoint = getTargetExtendedPoint(end, endAnchor, minDistance)

  // 检查从起点延伸点到终点锚点的直线是否与障碍物相交
  // 这样可以确保检测到目标组件的阻挡
  const directPath = [firstPoint, end]
  const hasObstacle = pathIntersectsObstacles(directPath, obstacles)

  if (!hasObstacle) {
    // 没有障碍物，检查是否需要转向
    if (needsIntermediatePoints(firstPoint, lastPoint, startAnchor, endAnchor)) {
      const intermediatePoints = generateIntermediatePoints(firstPoint, lastPoint, startAnchor, endAnchor)
      points.push(...intermediatePoints)
    } else {
      // 直接连接到目标延伸点
      points.push(lastPoint)
    }
  } else {
    // 有障碍物，生成绕行路径
    const bypassPoints = generateSimpleBypassPath(firstPoint, lastPoint, startAnchor, endAnchor, obstacles)
    points.push(...bypassPoints)
    points.push(lastPoint)
  }

  // 最后从延伸点连接到真正的锚点
  points.push(end)
  return points
}

// 生成简单的绕行路径（在两个延伸点之间）
const generateSimpleBypassPath = (
  start: { x: number; y: number },
  end: { x: number; y: number },
  startAnchor: string,
  endAnchor: string,
  obstacles: Array<{x: number, y: number, width: number, height: number, id: string}>
) => {
  const points: Array<{ x: number; y: number }> = []
  const margin = 30

  // 找到主要障碍物
  const mainObstacle = findMainObstacle(obstacles.filter(obs =>
    lineIntersectsRect(start, end, obs)
  ))

  if (!mainObstacle) {
    // 没有主要障碍物，使用简单路径
    if (needsIntermediatePoints(start, end, startAnchor, endAnchor)) {
      const intermediatePoints = generateIntermediatePoints(start, end, startAnchor, endAnchor)
      return intermediatePoints
    }
    return points
  }

  // 检查是否有其他连接线重合，如果有则添加偏移
  const offset = calculateLineOffset(start, end, startAnchor, endAnchor)

  // 如果有偏移，创建分叉效果
  if (offset > 0) {
    const branchedPoints = generateBranchedPath(start, end, startAnchor, endAnchor, offset, mainObstacle, margin)
    return branchedPoints
  }

  // 根据锚点方向选择绕行策略
  const startDir = getAnchorDirection(startAnchor)

  if (startDir === 'vertical') {
    // 垂直起点，水平绕行
    // 需要考虑目标锚点的位置，避免穿过目标组件
    const endDir = getAnchorDirection(endAnchor)

    if (endDir === 'horizontal' && endAnchor === 'left') {
      // 目标是左侧锚点，必须从右侧或上下绕行，不能从左侧
      if (start.x < mainObstacle.x) {
        // 起点在障碍物左侧，从左侧绕行
        const bypassX = mainObstacle.x - margin
        points.push({ x: bypassX, y: start.y })
        points.push({ x: bypassX, y: end.y })
      } else {
        // 起点在障碍物上下或右侧，从上下绕行
        if (start.y < mainObstacle.y + mainObstacle.height / 2) {
          const bypassY = mainObstacle.y - margin
          points.push({ x: start.x, y: bypassY })
          points.push({ x: end.x, y: bypassY })
        } else {
          const bypassY = mainObstacle.y + mainObstacle.height + margin
          points.push({ x: start.x, y: bypassY })
          points.push({ x: end.x, y: bypassY })
        }
      }
    } else if (endDir === 'horizontal' && endAnchor === 'right') {
      // 目标是右侧锚点，必须从左侧或上下绕行，不能从右侧
      if (start.x > mainObstacle.x + mainObstacle.width) {
        // 起点在障碍物右侧，从右侧绕行
        const bypassX = mainObstacle.x + mainObstacle.width + margin
        points.push({ x: bypassX, y: start.y })
        points.push({ x: bypassX, y: end.y })
      } else {
        // 起点在障碍物上下或左侧，从上下绕行
        if (start.y < mainObstacle.y + mainObstacle.height / 2) {
          const bypassY = mainObstacle.y - margin
          points.push({ x: start.x, y: bypassY })
          points.push({ x: end.x, y: bypassY })
        } else {
          const bypassY = mainObstacle.y + mainObstacle.height + margin
          points.push({ x: start.x, y: bypassY })
          points.push({ x: end.x, y: bypassY })
        }
      }
    } else {
      // 其他情况，使用原来的逻辑
      if (start.x < mainObstacle.x + mainObstacle.width / 2) {
        const bypassX = mainObstacle.x - margin
        points.push({ x: bypassX, y: start.y })
        points.push({ x: bypassX, y: end.y })
      } else {
        const bypassX = mainObstacle.x + mainObstacle.width + margin
        points.push({ x: bypassX, y: start.y })
        points.push({ x: bypassX, y: end.y })
      }
    }
  } else {
    // 水平起点，垂直绕行
    // 需要考虑目标锚点的位置，避免穿过目标组件
    const endDir = getAnchorDirection(endAnchor)

    if (endDir === 'vertical' && endAnchor === 'bottom') {
      // 目标是下方锚点，必须从上方或侧面绕行，不能从下方
      if (start.y < mainObstacle.y) {
        // 起点在障碍物上方，从上方绕行
        const bypassY = mainObstacle.y - margin
        points.push({ x: start.x, y: bypassY })
        points.push({ x: end.x, y: bypassY })
      } else {
        // 起点在障碍物侧面或下方，从侧面绕行
        if (start.x < mainObstacle.x + mainObstacle.width / 2) {
          // 从左侧绕行
          const bypassX = mainObstacle.x - margin
          points.push({ x: bypassX, y: start.y })
          points.push({ x: bypassX, y: end.y })
        } else {
          // 从右侧绕行
          const bypassX = mainObstacle.x + mainObstacle.width + margin
          points.push({ x: bypassX, y: start.y })
          points.push({ x: bypassX, y: end.y })
        }
      }
    } else if (endDir === 'vertical' && endAnchor === 'top') {
      // 目标是上方锚点，必须从下方或侧面绕行，不能从上方
      if (start.y > mainObstacle.y + mainObstacle.height) {
        // 起点在障碍物下方，从下方绕行
        const bypassY = mainObstacle.y + mainObstacle.height + margin
        points.push({ x: start.x, y: bypassY })
        points.push({ x: end.x, y: bypassY })
      } else {
        // 起点在障碍物侧面或上方，从侧面绕行
        if (start.x < mainObstacle.x + mainObstacle.width / 2) {
          const bypassX = mainObstacle.x - margin
          points.push({ x: bypassX, y: start.y })
          points.push({ x: bypassX, y: end.y })
        } else {
          const bypassX = mainObstacle.x + mainObstacle.width + margin
          points.push({ x: bypassX, y: start.y })
          points.push({ x: bypassX, y: end.y })
        }
      }
    } else {
      // 其他情况，使用原来的逻辑
      if (start.y < mainObstacle.y + mainObstacle.height / 2) {
        const bypassY = mainObstacle.y - margin
        points.push({ x: start.x, y: bypassY })
        points.push({ x: end.x, y: bypassY })
      } else {
        const bypassY = mainObstacle.y + mainObstacle.height + margin
        points.push({ x: start.x, y: bypassY })
        points.push({ x: end.x, y: bypassY })
      }
    }
  }

  return points
}

// 计算线条偏移量以避免重合
const calculateLineOffset = (
  start: { x: number; y: number },
  end: { x: number; y: number },
  startAnchor: string,
  endAnchor: string
) => {
  if (!props.allConnections || !props.connectionIndex) return 0

  const currentConnection = props.connection
  let overlappingCount = 0

  // 检查与其他连接线的重合情况
  for (let i = 0; i < props.connectionIndex; i++) {
    const otherConnection = props.allConnections[i]

    // 检查是否连接相同的组件和锚点
    if (isConnectionOverlapping(currentConnection, otherConnection)) {
      overlappingCount++
    }
  }

  // 根据重合数量计算偏移量
  return overlappingCount * 25 // 每个重合连接偏移25像素
}

// 检查两个连接是否重合
const isConnectionOverlapping = (
  conn1: ComponentConnection,
  conn2: ComponentConnection
) => {
  // 检查是否连接相同的组件对
  const sameDirection = (
    conn1.sourceComponent === conn2.sourceComponent &&
    conn1.targetComponent === conn2.targetComponent &&
    conn1.sourceAnchor.position === conn2.sourceAnchor.position &&
    conn1.targetAnchor.position === conn2.targetAnchor.position
  )

  const reverseDirection = (
    conn1.sourceComponent === conn2.targetComponent &&
    conn1.targetComponent === conn2.sourceComponent &&
    conn1.sourceAnchor.position === conn2.targetAnchor.position &&
    conn1.targetAnchor.position === conn2.sourceAnchor.position
  )

  return sameDirection || reverseDirection
}

// 生成分叉路径（在两个延伸点之间）
const generateBranchedPath = (
  start: { x: number; y: number },
  end: { x: number; y: number },
  startAnchor: string,
  endAnchor: string,
  offset: number,
  obstacle: {x: number, y: number, width: number, height: number} | null,
  margin: number
) => {
  const points: Array<{ x: number; y: number }> = []

  // 根据锚点方向创建分叉
  const startDir = getAnchorDirection(startAnchor)
  const endDir = getAnchorDirection(endAnchor)

  if (startDir === 'vertical') {
    // 垂直起点，创建水平分叉
    const branchX = start.x < end.x ? start.x + offset : start.x - offset

    if (obstacle) {
      // 有障碍物，需要绕行
      if (start.x < obstacle.x + obstacle.width / 2) {
        const bypassX = obstacle.x - margin - offset
        points.push({ x: bypassX, y: start.y })
        points.push({ x: bypassX, y: end.y })
      } else {
        const bypassX = obstacle.x + obstacle.width + margin + offset
        points.push({ x: bypassX, y: start.y })
        points.push({ x: bypassX, y: end.y })
      }
    } else {
      // 没有障碍物，创建L型路径
      if (endDir === 'horizontal') {
        // 目标也是水平方向，需要中间转向
        points.push({ x: branchX, y: start.y })
        points.push({ x: branchX, y: end.y })
      } else {
        // 目标是垂直方向，直接连接
        points.push({ x: end.x, y: start.y })
      }
    }
  } else {
    // 水平起点，创建垂直分叉
    const branchY = start.y < end.y ? start.y + offset : start.y - offset

    if (obstacle) {
      // 有障碍物，需要绕行
      if (start.y < obstacle.y + obstacle.height / 2) {
        const bypassY = obstacle.y - margin - offset
        points.push({ x: start.x, y: bypassY })
        points.push({ x: end.x, y: bypassY })
      } else {
        const bypassY = obstacle.y + obstacle.height + margin + offset
        points.push({ x: start.x, y: bypassY })
        points.push({ x: end.x, y: bypassY })
      }
    } else {
      // 没有障碍物，创建L型路径
      if (endDir === 'vertical') {
        // 目标也是垂直方向，需要中间转向
        points.push({ x: start.x, y: branchY })
        points.push({ x: end.x, y: branchY })
      } else {
        // 目标是水平方向，直接连接
        points.push({ x: start.x, y: end.y })
      }
    }
  }

  return points
}

// 获取所有组件的边界框
const getAllComponentBounds = () => {
  const obstacles: Array<{x: number, y: number, width: number, height: number, id: string}> = []
  const margin = 20 // 避障边距

  // 获取源组件和目标组件的ID
  const sourceId = props.connection.sourceComponent
  const targetId = props.connection.targetComponent

  // 如果有所有组件的信息，遍历所有组件
  if (props.allComponents && props.allComponents.length > 0) {
    for (const component of props.allComponents) {
      // 添加所有组件作为障碍物，包括源组件和目标组件
      obstacles.push({
        id: component.id,
        x: component.x - margin,
        y: component.y - margin,
        width: component.width + margin * 2,
        height: component.height + margin * 2
      })
    }
  } else if (props.sourceComponent && props.targetComponent) {
    // 如果没有所有组件信息，添加源组件和目标组件
    const sourceComp = props.sourceComponent
    const targetComp = props.targetComponent

    // 添加源组件边界
    obstacles.push({
      id: sourceId,
      x: sourceComp.x - margin,
      y: sourceComp.y - margin,
      width: sourceComp.width + margin * 2,
      height: sourceComp.height + margin * 2
    })

    // 添加目标组件边界
    obstacles.push({
      id: targetId,
      x: targetComp.x - margin,
      y: targetComp.y - margin,
      width: targetComp.width + margin * 2,
      height: targetComp.height + margin * 2
    })
  }

  return obstacles
}



// 根据锚点方向延伸点
const getExtendedPoint = (
  point: { x: number; y: number },
  anchor: string,
  distance: number,
  reverse: boolean = false
) => {
  const multiplier = reverse ? -1 : 1

  switch (anchor) {
    case 'left':
      return { x: point.x - distance * multiplier, y: point.y }
    case 'right':
      return { x: point.x + distance * multiplier, y: point.y }
    case 'top':
      return { x: point.x, y: point.y - distance * multiplier }
    case 'bottom':
      return { x: point.x, y: point.y + distance * multiplier }
    default:
      return point
  }
}

// 获取目标锚点的延伸点（向外延伸，用于连接线接近）
const getTargetExtendedPoint = (
  point: { x: number; y: number },
  anchor: string,
  distance: number
) => {
  // 目标锚点向外延伸，连接线从外面接近锚点
  // 注意：这里的"向外"是指远离组件中心的方向
  switch (anchor) {
    case 'left':
      return { x: point.x - distance, y: point.y } // 向左延伸（连接线从左侧接近）
    case 'right':
      return { x: point.x + distance, y: point.y } // 向右延伸（连接线从右侧接近）
    case 'top':
      return { x: point.x, y: point.y - distance } // 向上延伸（连接线从上方接近）
    case 'bottom':
      return { x: point.x, y: point.y + distance } // 向下延伸（连接线从下方接近）
    default:
      return point
  }
}



// 检查路径是否与障碍物相交
const pathIntersectsObstacles = (
  path: Array<{ x: number; y: number }>,
  obstacles: Array<{x: number, y: number, width: number, height: number, id?: string}>
) => {
  for (let i = 0; i < path.length - 1; i++) {
    const start = path[i]
    const end = path[i + 1]

    for (const obstacle of obstacles) {
      if (lineIntersectsRect(start, end, obstacle)) {
        return true
      }
    }
  }
  return false
}

// 检查线段是否与矩形相交
const lineIntersectsRect = (
  start: { x: number; y: number },
  end: { x: number; y: number },
  rect: {x: number, y: number, width: number, height: number}
) => {
  // 使用线段与矩形相交算法
  const { x: rx, y: ry, width: rw, height: rh } = rect

  // 检查线段端点是否在矩形内
  if (pointInRect(start, rect) || pointInRect(end, rect)) {
    return true
  }

  // 检查线段是否与矩形的四条边相交
  const rectLines = [
    { start: { x: rx, y: ry }, end: { x: rx + rw, y: ry } }, // 上边
    { start: { x: rx + rw, y: ry }, end: { x: rx + rw, y: ry + rh } }, // 右边
    { start: { x: rx + rw, y: ry + rh }, end: { x: rx, y: ry + rh } }, // 下边
    { start: { x: rx, y: ry + rh }, end: { x: rx, y: ry } } // 左边
  ]

  for (const rectLine of rectLines) {
    if (linesIntersect(start, end, rectLine.start, rectLine.end)) {
      return true
    }
  }

  return false
}

// 检查点是否在矩形内
const pointInRect = (
  point: { x: number; y: number },
  rect: {x: number, y: number, width: number, height: number}
) => {
  return point.x >= rect.x &&
         point.x <= rect.x + rect.width &&
         point.y >= rect.y &&
         point.y <= rect.y + rect.height
}

// 检查两条线段是否相交
const linesIntersect = (
  p1: { x: number; y: number }, p2: { x: number; y: number },
  p3: { x: number; y: number }, p4: { x: number; y: number }
) => {
  const denom = (p4.y - p3.y) * (p2.x - p1.x) - (p4.x - p3.x) * (p2.y - p1.y)
  if (denom === 0) return false // 平行线

  const ua = ((p4.x - p3.x) * (p1.y - p3.y) - (p4.y - p3.y) * (p1.x - p3.x)) / denom
  const ub = ((p2.x - p1.x) * (p1.y - p3.y) - (p2.y - p1.y) * (p1.x - p3.x)) / denom

  return ua >= 0 && ua <= 1 && ub >= 0 && ub <= 1
}

// 检查是否需要中间点
const needsIntermediatePoints = (
  start: { x: number; y: number },
  end: { x: number; y: number },
  startAnchor: string,
  endAnchor: string
) => {
  const startDir = getAnchorDirection(startAnchor)
  const endDir = getAnchorDirection(endAnchor)

  // 如果方向不同，需要中间点来转向
  return startDir !== endDir
}

// 生成中间转向点
const generateIntermediatePoints = (
  start: { x: number; y: number },
  end: { x: number; y: number },
  startAnchor: string,
  endAnchor: string
) => {
  const points: Array<{ x: number; y: number }> = []
  const startDir = getAnchorDirection(startAnchor)
  const endDir = getAnchorDirection(endAnchor)

  if (startDir === 'horizontal' && endDir === 'vertical') {
    // 水平到垂直：先水平，后垂直
    points.push({ x: end.x, y: start.y })
  } else if (startDir === 'vertical' && endDir === 'horizontal') {
    // 垂直到水平：先垂直，后水平
    points.push({ x: start.x, y: end.y })
  }

  return points
}

// 生成避障路径
const generateAvoidancePath = (
  start: { x: number; y: number },
  end: { x: number; y: number },
  obstacles: Array<{x: number, y: number, width: number, height: number}>,
  startAnchor: string,
  endAnchor: string
) => {
  const points: Array<{ x: number; y: number }> = []
  const margin = 25

  // 找到阻挡路径的障碍物
  const blockingObstacles = obstacles.filter(obstacle =>
    lineIntersectsRect(start, end, obstacle)
  )

  if (blockingObstacles.length === 0) {
    // 没有阻挡，直接连接
    return points
  }

  // 选择最大的阻挡障碍物作为主要避障目标
  let mainObstacle = blockingObstacles[0]
  for (const obstacle of blockingObstacles) {
    if (obstacle.width * obstacle.height > mainObstacle.width * mainObstacle.height) {
      mainObstacle = obstacle
    }
  }

  // 根据锚点方向和障碍物位置决定绕行策略
  const startDir = getAnchorDirection(startAnchor)
  const endDir = getAnchorDirection(endAnchor)

  // 计算绕行路径
  if (startDir === 'horizontal' && endDir === 'horizontal') {
    // 水平到水平：垂直绕行
    if (start.y < mainObstacle.y + mainObstacle.height / 2) {
      // 从上方绕行
      const bypassY = mainObstacle.y - margin
      points.push({ x: start.x, y: bypassY })
      points.push({ x: end.x, y: bypassY })
    } else {
      // 从下方绕行
      const bypassY = mainObstacle.y + mainObstacle.height + margin
      points.push({ x: start.x, y: bypassY })
      points.push({ x: end.x, y: bypassY })
    }
  } else if (startDir === 'vertical' && endDir === 'vertical') {
    // 垂直到垂直：水平绕行
    if (start.x < mainObstacle.x + mainObstacle.width / 2) {
      // 从左侧绕行
      const bypassX = mainObstacle.x - margin
      points.push({ x: bypassX, y: start.y })
      points.push({ x: bypassX, y: end.y })
    } else {
      // 从右侧绕行
      const bypassX = mainObstacle.x + mainObstacle.width + margin
      points.push({ x: bypassX, y: start.y })
      points.push({ x: bypassX, y: end.y })
    }
  } else {
    // 混合方向：选择最佳绕行路径
    const dx = end.x - start.x
    const dy = end.y - start.y

    if (Math.abs(dx) > Math.abs(dy)) {
      // 主要是水平移动，垂直绕行
      if (start.y < mainObstacle.y + mainObstacle.height / 2) {
        const bypassY = mainObstacle.y - margin
        points.push({ x: start.x, y: bypassY })
        points.push({ x: end.x, y: bypassY })
      } else {
        const bypassY = mainObstacle.y + mainObstacle.height + margin
        points.push({ x: start.x, y: bypassY })
        points.push({ x: end.x, y: bypassY })
      }
    } else {
      // 主要是垂直移动，水平绕行
      if (start.x < mainObstacle.x + mainObstacle.width / 2) {
        const bypassX = mainObstacle.x - margin
        points.push({ x: bypassX, y: start.y })
        points.push({ x: bypassX, y: end.y })
      } else {
        const bypassX = mainObstacle.x + mainObstacle.width + margin
        points.push({ x: bypassX, y: start.y })
        points.push({ x: bypassX, y: end.y })
      }
    }
  }

  return points
}

// 生成智能避障路径
const generateSmartAvoidancePath = (
  start: { x: number; y: number },
  end: { x: number; y: number },
  obstacles: Array<{x: number, y: number, width: number, height: number, id: string}>,
  startAnchor: string,
  endAnchor: string,
  sourceObstacle?: {x: number, y: number, width: number, height: number, id: string},
  targetObstacle?: {x: number, y: number, width: number, height: number, id: string}
) => {
  const points: Array<{ x: number; y: number }> = []
  const margin = 30

  // 检查直接路径是否被阻挡
  const blockingObstacles = obstacles.filter(obstacle =>
    lineIntersectsRect(start, end, obstacle)
  )

  if (blockingObstacles.length === 0) {
    // 直接路径没有阻挡，检查是否需要转向
    if (needsIntermediatePoints(start, end, startAnchor, endAnchor)) {
      const intermediatePoints = generateIntermediatePoints(start, end, startAnchor, endAnchor)
      points.push(...intermediatePoints)
    }
    return points
  }

  // 有阻挡，需要绕行
  const startDir = getAnchorDirection(startAnchor)
  const endDir = getAnchorDirection(endAnchor)

  // 找到主要的阻挡障碍物
  const mainObstacle = findMainObstacle(blockingObstacles)
  if (!mainObstacle) {
    // 没有主要障碍物，使用简单路径
    if (needsIntermediatePoints(start, end, startAnchor, endAnchor)) {
      points.push(...generateIntermediatePoints(start, end, startAnchor, endAnchor))
    }
    return points
  }

  // 根据锚点方向和障碍物位置计算绕行路径
  if (startDir === 'vertical' && endDir === 'horizontal') {
    // 从垂直锚点到水平锚点
    points.push(...generateVerticalToHorizontalPath(start, end, startAnchor, endAnchor, mainObstacle, margin))
  } else if (startDir === 'horizontal' && endDir === 'vertical') {
    // 从水平锚点到垂直锚点
    points.push(...generateHorizontalToVerticalPath(start, end, startAnchor, endAnchor, mainObstacle, margin))
  } else if (startDir === 'vertical' && endDir === 'vertical') {
    // 垂直到垂直
    points.push(...generateVerticalToVerticalPath(start, end, startAnchor, endAnchor, mainObstacle, margin))
  } else if (startDir === 'horizontal' && endDir === 'horizontal') {
    // 水平到水平
    points.push(...generateHorizontalToHorizontalPath(start, end, startAnchor, endAnchor, mainObstacle, margin))
  } else {
    // 其他情况，使用通用绕行
    points.push(...generateGenericAvoidancePath(start, end, mainObstacle, margin))
  }

  return points
}

// 获取到锚点的中间点
const getIntermediatePointToAnchor = (
  from: { x: number; y: number },
  to: { x: number; y: number },
  toAnchor: string
) => {
  // 根据目标锚点的方向，确定中间点的位置
  switch (toAnchor) {
    case 'left':
      // 目标是左锚点，中间点应该在目标点的左侧
      return { x: to.x - 30, y: from.y }
    case 'right':
      // 目标是右锚点，中间点应该在目标点的右侧
      return { x: to.x + 30, y: from.y }
    case 'top':
      // 目标是上锚点，中间点应该在目标点的上方
      return { x: from.x, y: to.y - 30 }
    case 'bottom':
      // 目标是下锚点，中间点应该在目标点的下方
      return { x: from.x, y: to.y + 30 }
    default:
      return null
  }
}

// 找到主要障碍物
const findMainObstacle = (obstacles: Array<{x: number, y: number, width: number, height: number}>) => {
  if (obstacles.length === 0) return null

  // 返回面积最大的障碍物
  return obstacles.reduce((max, current) => {
    const maxArea = max.width * max.height
    const currentArea = current.width * current.height
    return currentArea > maxArea ? current : max
  })
}

// 垂直到水平的路径
const generateVerticalToHorizontalPath = (
  start: { x: number; y: number },
  end: { x: number; y: number },
  startAnchor: string,
  endAnchor: string,
  obstacle: {x: number, y: number, width: number, height: number},
  margin: number
) => {
  const points: Array<{ x: number; y: number }> = []

  // 根据起点位置决定绕行方向
  if (startAnchor === 'top') {
    // 从上方出发，需要绕过障碍物
    if (start.x < obstacle.x + obstacle.width / 2) {
      // 从左侧绕行
      const bypassX = obstacle.x - margin
      points.push({ x: bypassX, y: start.y })
      // 根据目标锚点方向调整最后的连接
      if (endAnchor === 'left') {
        points.push({ x: bypassX, y: end.y })
      } else {
        points.push({ x: bypassX, y: end.y })
        // 如果目标是右锚点，需要从左侧接近
      }
    } else {
      // 从右侧绕行
      const bypassX = obstacle.x + obstacle.width + margin
      points.push({ x: bypassX, y: start.y })
      points.push({ x: bypassX, y: end.y })
    }
  } else {
    // 从下方出发
    if (start.x < obstacle.x + obstacle.width / 2) {
      const bypassX = obstacle.x - margin
      points.push({ x: bypassX, y: start.y })
      points.push({ x: bypassX, y: end.y })
    } else {
      const bypassX = obstacle.x + obstacle.width + margin
      points.push({ x: bypassX, y: start.y })
      points.push({ x: bypassX, y: end.y })
    }
  }

  return points
}

// 水平到垂直的路径
const generateHorizontalToVerticalPath = (
  start: { x: number; y: number },
  end: { x: number; y: number },
  startAnchor: string,
  endAnchor: string,
  obstacle: {x: number, y: number, width: number, height: number},
  margin: number
) => {
  const points: Array<{ x: number; y: number }> = []

  if (startAnchor === 'left') {
    // 从左侧出发
    if (start.y < obstacle.y + obstacle.height / 2) {
      // 从上方绕行
      const bypassY = obstacle.y - margin
      points.push({ x: start.x, y: bypassY })
      points.push({ x: end.x, y: bypassY })
    } else {
      // 从下方绕行
      const bypassY = obstacle.y + obstacle.height + margin
      points.push({ x: start.x, y: bypassY })
      points.push({ x: end.x, y: bypassY })
    }
  } else {
    // 从右侧出发
    if (start.y < obstacle.y + obstacle.height / 2) {
      const bypassY = obstacle.y - margin
      points.push({ x: start.x, y: bypassY })
      points.push({ x: end.x, y: bypassY })
    } else {
      const bypassY = obstacle.y + obstacle.height + margin
      points.push({ x: start.x, y: bypassY })
      points.push({ x: end.x, y: bypassY })
    }
  }

  return points
}

// 垂直到垂直的路径
const generateVerticalToVerticalPath = (
  start: { x: number; y: number },
  end: { x: number; y: number },
  startAnchor: string,
  endAnchor: string,
  obstacle: {x: number, y: number, width: number, height: number},
  margin: number
) => {
  const points: Array<{ x: number; y: number }> = []
  const midY = (start.y + end.y) / 2

  // 检查中间水平线是否与障碍物相交
  if (midY > obstacle.y - margin && midY < obstacle.y + obstacle.height + margin) {
    // 需要绕行
    if (startAnchor === 'top') {
      const bypassY = obstacle.y - margin
      points.push({ x: start.x, y: bypassY })
      points.push({ x: end.x, y: bypassY })
    } else {
      const bypassY = obstacle.y + obstacle.height + margin
      points.push({ x: start.x, y: bypassY })
      points.push({ x: end.x, y: bypassY })
    }
  } else {
    // 简单的L型路径
    points.push({ x: start.x, y: midY })
    points.push({ x: end.x, y: midY })
  }

  return points
}

// 水平到水平的路径
const generateHorizontalToHorizontalPath = (
  start: { x: number; y: number },
  end: { x: number; y: number },
  startAnchor: string,
  endAnchor: string,
  obstacle: {x: number, y: number, width: number, height: number},
  margin: number
) => {
  const points: Array<{ x: number; y: number }> = []
  const midX = (start.x + end.x) / 2

  // 检查中间垂直线是否与障碍物相交
  if (midX > obstacle.x - margin && midX < obstacle.x + obstacle.width + margin) {
    // 需要绕行
    if (start.y < obstacle.y + obstacle.height / 2) {
      const bypassY = obstacle.y - margin
      points.push({ x: start.x, y: bypassY })
      points.push({ x: end.x, y: bypassY })
    } else {
      const bypassY = obstacle.y + obstacle.height + margin
      points.push({ x: start.x, y: bypassY })
      points.push({ x: end.x, y: bypassY })
    }
  } else {
    // 简单的L型路径
    points.push({ x: midX, y: start.y })
    points.push({ x: midX, y: end.y })
  }

  return points
}

// 通用避障路径
const generateGenericAvoidancePath = (
  start: { x: number; y: number },
  end: { x: number; y: number },
  obstacle: {x: number, y: number, width: number, height: number},
  margin: number
) => {
  const points: Array<{ x: number; y: number }> = []

  // 选择最短的绕行路径
  const dx = end.x - start.x
  const dy = end.y - start.y

  if (Math.abs(dx) > Math.abs(dy)) {
    // 主要是水平移动，垂直绕行
    if (start.y < obstacle.y + obstacle.height / 2) {
      const bypassY = obstacle.y - margin
      points.push({ x: start.x, y: bypassY })
      points.push({ x: end.x, y: bypassY })
    } else {
      const bypassY = obstacle.y + obstacle.height + margin
      points.push({ x: start.x, y: bypassY })
      points.push({ x: end.x, y: bypassY })
    }
  } else {
    // 主要是垂直移动，水平绕行
    if (start.x < obstacle.x + obstacle.width / 2) {
      const bypassX = obstacle.x - margin
      points.push({ x: bypassX, y: start.y })
      points.push({ x: bypassX, y: end.y })
    } else {
      const bypassX = obstacle.x + obstacle.width + margin
      points.push({ x: bypassX, y: start.y })
      points.push({ x: bypassX, y: end.y })
    }
  }

  return points
}

// 获取锚点方向
const getAnchorDirection = (anchor: string) => {
  if (anchor === 'left' || anchor === 'right') return 'horizontal'
  if (anchor === 'top' || anchor === 'bottom') return 'vertical'
  return 'mixed'
}

// 计算箭头在连接线上的分布位置
const arrowPositions = computed(() => {
  if (pathPoints.value.length < 2) return []

  const positions: Array<{ x: number; y: number; angle: number }> = []
  const points = pathPoints.value

  // 计算总路径长度
  let totalLength = 0
  const segmentLengths: number[] = []

  for (let i = 0; i < points.length - 1; i++) {
    const dx = points[i + 1].x - points[i].x
    const dy = points[i + 1].y - points[i].y
    const length = Math.sqrt(dx * dx + dy * dy)
    segmentLengths.push(length)
    totalLength += length
  }

  if (totalLength < 50) return [] // 路径太短不显示箭头

  // 计算箭头数量和位置
  const arrowCount = Math.min(3, Math.floor(totalLength / 80))
  const segmentDistance = totalLength / (arrowCount + 1)

  let currentDistance = segmentDistance
  let segmentIndex = 0
  let segmentProgress = 0

  for (let i = 0; i < arrowCount; i++) {
    // 找到当前距离对应的线段
    while (segmentIndex < segmentLengths.length &&
           segmentProgress + segmentLengths[segmentIndex] < currentDistance) {
      segmentProgress += segmentLengths[segmentIndex]
      segmentIndex++
    }

    if (segmentIndex < segmentLengths.length) {
      const remainingDistance = currentDistance - segmentProgress
      const ratio = remainingDistance / segmentLengths[segmentIndex]

      const startPoint = points[segmentIndex]
      const endPoint = points[segmentIndex + 1]

      const x = startPoint.x + (endPoint.x - startPoint.x) * ratio
      const y = startPoint.y + (endPoint.y - startPoint.y) * ratio
      const angle = Math.atan2(endPoint.y - startPoint.y, endPoint.x - startPoint.x) * (180 / Math.PI)

      positions.push({ x, y, angle })
    }

    currentDistance += segmentDistance
  }

  return positions
})

// 计算箭头的多边形点
const getArrowPoints = (pos: { x: number; y: number; angle: number }) => {
  const size = props.connection.style.arrowSize || 8
  const halfSize = size / 2

  // 箭头的基本形状点（指向右）
  const basePoints = [
    { x: halfSize, y: 0 },      // 箭头尖端
    { x: -halfSize, y: -halfSize/2 }, // 左上
    { x: -halfSize/2, y: 0 },   // 左中
    { x: -halfSize, y: halfSize/2 }   // 左下
  ]

  // 旋转和平移
  const radians = (pos.angle * Math.PI) / 180
  const cos = Math.cos(radians)
  const sin = Math.sin(radians)

  return basePoints.map(point => {
    const rotatedX = point.x * cos - point.y * sin
    const rotatedY = point.x * sin + point.y * cos
    return `${pos.x + rotatedX},${pos.y + rotatedY}`
  }).join(' ')
}

// 获取锚点位置
const getAnchorPoint = (component: ConfigurationComponent, position: string) => {
  const { x, y, width, height } = component

  switch (position) {
    case 'top':
      return { x: x + width / 2, y }
    case 'right':
      return { x: x + width, y: y + height / 2 }
    case 'bottom':
      return { x: x + width / 2, y: y + height }
    case 'left':
      return { x, y: y + height / 2 }
    case 'center':
    default:
      return { x: x + width / 2, y: y + height / 2 }
  }
}

// 生成曲线路径
const generateCurvedPath = (start: { x: number; y: number }, end: { x: number; y: number }) => {
  const dx = end.x - start.x

  // 控制点偏移
  const offset = Math.abs(dx) * 0.5
  
  const cp1x = start.x + offset
  const cp1y = start.y
  const cp2x = end.x - offset
  const cp2y = end.y
  
  return `M ${start.x} ${start.y} C ${cp1x} ${cp1y}, ${cp2x} ${cp2y}, ${end.x} ${end.y}`
}

// 生成折线路径
const generatePolylinePath = (start: { x: number; y: number }, end: { x: number; y: number }) => {
  const midX = (start.x + end.x) / 2
  
  return `M ${start.x} ${start.y} L ${midX} ${start.y} L ${midX} ${end.y} L ${end.x} ${end.y}`
}

// 拖拽状态
const isDragging = ref(false)
const dragHandleIndex = ref(-1)
const dragStartPos = ref({ x: 0, y: 0 })

// 开始拖拽路径点
const startDragHandle = (event: MouseEvent, index: number) => {
  isDragging.value = true
  dragHandleIndex.value = index
  dragStartPos.value = { x: event.clientX, y: event.clientY }

  document.addEventListener('mousemove', onDragHandle)
  document.addEventListener('mouseup', stopDragHandle)
}

// 拖拽路径点
const onDragHandle = (event: MouseEvent) => {
  if (!isDragging.value || dragHandleIndex.value === -1) return

  const dx = event.clientX - dragStartPos.value.x
  const dy = event.clientY - dragStartPos.value.y

  // 更新路径点位置
  const newPathPoints = [...(props.connection.pathPoints || [])]
  if (newPathPoints[dragHandleIndex.value]) {
    newPathPoints[dragHandleIndex.value] = {
      x: newPathPoints[dragHandleIndex.value].x + dx,
      y: newPathPoints[dragHandleIndex.value].y + dy
    }

    // 发送更新事件
    const updatedConnection = {
      ...props.connection,
      pathPoints: newPathPoints
    }
    emit('update', updatedConnection)
  }

  dragStartPos.value = { x: event.clientX, y: event.clientY }
}

// 停止拖拽
const stopDragHandle = () => {
  isDragging.value = false
  dragHandleIndex.value = -1

  document.removeEventListener('mousemove', onDragHandle)
  document.removeEventListener('mouseup', stopDragHandle)
}

// 选择连接线
const selectLine = () => {
  emit('select', props.connection)
}
</script>

<style scoped>
.connection-line {
  cursor: pointer;
  /* 使用 transform 而不是 filter 来提高性能 */
  will-change: transform;
}

.connection-line:hover path {
  opacity: 0.8;
  /* 减少复杂的 filter 效果 */
}

.connection-line.selected {
  /* 使用 transform 替代 filter 提高性能 */
  transform: translateZ(0); /* 启用硬件加速 */
}

.connection-line.selected path {
  stroke-width: calc(var(--stroke-width, 4) + 2);
  /* 添加简单的阴影效果 */
  filter: drop-shadow(0 0 4px rgba(64, 158, 255, 0.3));
}

.animated {
  animation: dash 2s linear infinite;
}

@keyframes dash {
  to {
    stroke-dashoffset: -20;
  }
}

/* 编辑手柄样式 */
.connection-line circle {
  transition: transform 0.15s ease; /* 缩短过渡时间 */
  will-change: transform;
}

.connection-line circle:hover {
  transform: scale(1.2) translateZ(0); /* 启用硬件加速 */
  fill: #67c23a;
}

/* 圆滑连接线样式 */
.connection-line path {
  stroke-linecap: round;
  stroke-linejoin: round;
  /* 减少过渡时间，提高响应性 */
  transition: stroke-width 0.15s ease, opacity 0.15s ease;
  /* 启用硬件加速 */
  will-change: stroke-width, opacity;
}

/* 重合连接线的视觉区分 */
.connection-line.overlapping {
  opacity: 0.9;
}

.connection-line.overlapping:hover {
  opacity: 1;
  /* 使用 transform 而不是 z-index */
  transform: translateZ(1px);
}

/* 性能优化：减少重绘 */
.connection-line * {
  backface-visibility: hidden;
  perspective: 1000px;
}
</style>