import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { debounce, throttle } from 'lodash-es'
import type { 
  ConfigurationProject, 
  ConfigurationComponent, 
  EditorState,
  ComponentCategory
} from '../types'

// 组态项目存储
export const useConfigurationStore = defineStore('configuration', () => {
  // 项目列表
  const projectList = ref<ConfigurationProject[]>([])
  
  // 当前编辑的项目
  const currentProject = ref<ConfigurationProject | null>(null)
  
  // 编辑器状态
  const editorState = ref<EditorState>({
    selectedComponents: [],
    clipboard: [],
    history: {
      past: [],
      present: {} as ConfigurationProject,
      future: []
    },
    zoom: 1,
    grid: {
      show: true,
      size: 10,
      snap: true
    },
    rulers: true,
    layers: true
  })

  // 性能监控
  const performanceMetrics = ref({
    componentCount: 0,
    connectionCount: 0,
    renderTime: 0,
    memoryUsage: 0,
    lastUpdateTime: Date.now()
  })

  // 历史记录配置
  const HISTORY_LIMIT = 30 // 减少历史记录限制以节省内存
  const CACHE_CLEANUP_INTERVAL = 5 * 60 * 1000 // 5分钟清理一次缓存

  // 组件库
  const componentLibrary = ref<ComponentCategory[]>([
    {
      id: 'basic',
      name: '基础组件',
      icon: 'icon-basic',
      components: [
        {
          id: 'text',
          name: '文本',
          icon: 'icon-text',
          type: 'text',
          preview: '/src/assets/component-previews/text.svg',
          defaultProps: {
            width: 120,
            height: 40,
            style: {
              fontSize: 14,
              fontColor: '#333333',
              textAlign: 'left'
            },
            data: {
              static: '文本内容'
            }
          }
        },
        {
          id: 'image',
          name: '图片',
          icon: 'icon-image',
          type: 'image',
          preview: '/src/assets/component-previews/image.svg',
          defaultProps: {
            width: 200,
            height: 150,
            style: {
              borderRadius: 0
            },
            data: {
              static: ''
            }
          }
        },
        {
          id: 'shape',
          name: '形状',
          icon: 'icon-shape',
          type: 'shape',
          preview: '/src/assets/component-previews/shape.svg',
          defaultProps: {
            width: 100,
            height: 100,
            style: {
              backgroundColor: '#409EFF',
              borderRadius: 0
            }
          }
        },
        {
          id: 'button',
          name: '按钮',
          icon: 'icon-button',
          type: 'button',
          preview: '/src/assets/component-previews/button.svg',
          defaultProps: {
            width: 100,
            height: 40,
            style: {
              backgroundColor: '#409EFF',
              borderRadius: 4,
              fontColor: '#FFFFFF',
              fontSize: 14,
              textAlign: 'center'
            },
            data: {
              static: '按钮'
            }
          }
        }
      ]
    },
    {
      id: 'charts',
      name: '图表组件',
      icon: 'icon-chart',
      components: [
        {
          id: 'lineChart',
          name: '折线图',
          icon: 'icon-line-chart',
          type: 'chart',
          preview: '/src/assets/component-previews/line-chart.svg',
          defaultProps: {
            width: 400,
            height: 300,
            data: {
              static: {
                xAxis: {
                  type: 'category',
                  data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                },
                yAxis: {
                  type: 'value'
                },
                series: [{
                  data: [150, 230, 224, 218, 135, 147, 260],
                  type: 'line'
                }]
              }
            }
          }
        },
        {
          id: 'barChart',
          name: '柱状图',
          icon: 'icon-bar-chart',
          type: 'chart',
          preview: '/src/assets/component-previews/bar-chart.svg',
          defaultProps: {
            width: 400,
            height: 300,
            data: {
              static: {
                xAxis: {
                  type: 'category',
                  data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
                },
                yAxis: {
                  type: 'value'
                },
                series: [{
                  data: [120, 200, 150, 80, 70, 110, 130],
                  type: 'bar'
                }]
              }
            }
          }
        },
        {
          id: 'pieChart',
          name: '饼图',
          icon: 'icon-pie-chart',
          type: 'chart',
          preview: '/src/assets/component-previews/pie-chart.svg',
          defaultProps: {
            width: 400,
            height: 300,
            data: {
              static: {
                series: [{
                  type: 'pie',
                  radius: '50%',
                  data: [
                    { value: 1048, name: '类别A' },
                    { value: 735, name: '类别B' },
                    { value: 580, name: '类别C' },
                    { value: 484, name: '类别D' },
                    { value: 300, name: '类别E' }
                  ]
                }]
              }
            }
          }
        },
        {
          id: 'gauge',
          name: '仪表盘',
          icon: 'icon-gauge',
          type: 'gauge',
          preview: '/src/assets/component-previews/gauge.svg',
          defaultProps: {
            width: 300,
            height: 300,
            data: {
              static: {
                series: [{
                  type: 'gauge',
                  progress: {
                    show: true
                  },
                  detail: {
                    valueAnimation: true,
                    formatter: '{value}%'
                  },
                  data: [{ value: 70 }]
                }]
              }
            }
          }
        }
      ]
    },
    {
      id: 'industrial',
      name: '工业组件',
      icon: 'icon-industrial',
      components: [
        {
          id: 'tank',
          name: '储罐',
          icon: 'icon-tank',
          type: 'model3d',
          preview: '/src/assets/component-previews/tank.svg',
          defaultProps: {
            width: 200,
            height: 300,
            data: {
              static: {
                model: 'tank',
                fillLevel: 50,
                dataSource: 'static',
                dataPointId: '',
                maxValue: 1000,
                currentValue: 500,
                unit: 'L'
              }
            }
          }
        },
        {
          id: 'pump',
          name: '水泵',
          icon: 'icon-pump',
          type: 'model3d',
          preview: '/src/assets/component-previews/pump.svg',
          defaultProps: {
            width: 150,
            height: 150,
            data: {
              static: {
                model: 'pump',
                status: 'running'
              }
            }
          }
        },
        {
          id: 'valve',
          name: '阀门',
          icon: 'icon-valve',
          type: 'model3d',
          preview: '/src/assets/component-previews/valve.svg',
          defaultProps: {
            width: 100,
            height: 100,
            data: {
              static: {
                model: 'valve',
                status: 'open'
              }
            }
          }
        },
        {
          id: 'pipe',
          name: '管道',
          icon: 'icon-pipe',
          type: 'model3d',
          preview: '/src/assets/component-previews/pipe.svg',
          defaultProps: {
            width: 300,
            height: 50,
            data: {
              static: {
                model: 'pipe',
                flow: true,
                flowDirection: 'right',
                flowSpeed: 5
              }
            }
          }
        }
      ]
    },
    {
      id: 'cooling',
      name: '冷却设备',
      icon: 'icon-cooling',
      components: [
        {
          id: 'hyperbolicCoolingTower',
          name: '双曲线冷却塔',
          icon: 'icon-tower',
          type: 'hyperbolicCoolingTower',
          preview: '/src/assets/component-previews/hyperbolic-cooling-tower.svg',
          defaultProps: {
            width: 120,
            height: 160,
            data: {
              static: {
                isRunning: true,
                temperature: 35,
                efficiency: 85,
                towerColor: '#8db4d6',
                baseColor: '#a0a0a0',
                pipeColor: '#4a90e2',
                textColor: '#333'
              }
            }
          }
        },
        {
          id: 'mechanicalCoolingTower',
          name: '机械通风冷却塔',
          icon: 'icon-fan-tower',
          type: 'mechanicalCoolingTower',
          preview: '/src/assets/component-previews/mechanical-cooling-tower.svg',
          defaultProps: {
            width: 140,
            height: 180,
            data: {
              static: {
                isRunning: true,
                temperature: 32,
                fanSpeed: 1200,
                efficiency: 88,
                towerColor: '#a8c8ec',
                baseColor: '#a0a0a0',
                fanHousingColor: '#d0d0d0',
                fanBladeColor: '#e8e8e8',
                fanCenterColor: '#666',
                motorColor: '#4a4a4a',
                pipeColor: '#4a90e2',
                textColor: '#333'
              }
            }
          }
        },
        {
          id: 'condenser',
          name: '凝汽器',
          icon: 'icon-condenser',
          type: 'condenser',
          preview: '/src/assets/component-previews/condenser.svg',
          defaultProps: {
            width: 200,
            height: 120,
            data: {
              static: {
                isRunning: true,
                pressure: 8.5,
                temperature: 45,
                efficiency: 92,
                vacuumLevel: 95,
                shellColor: '#b8d4f0',
                pipeColor: '#4a90e2',
                waterPipeColor: '#2e7bd6',
                waterColor: '#4da6ff',
                pumpColor: '#666',
                sensorColor: '#ff6b35',
                textColor: '#333'
              }
            }
          }
        }
      ]
    },
    {
      id: 'cooling25d',
      name: '2.5D冷却设备',
      icon: 'icon-3d-cooling',
      components: [
        {
          id: 'coolingTower25d',
          name: '2.5D冷却塔',
          icon: 'icon-tower-3d',
          type: 'coolingTower25d',
          preview: '/src/assets/component-previews/cooling-tower-25d.svg',
          defaultProps: {
            width: 180,
            height: 200,
            data: {
              static: {
                isRunning: true,
                temperature: 28,
                efficiency: 92,
                flowRate: 1500,
                towerColor: '#5dade2',
                pipeColor: '#3498db',
                darkPipeColor: '#2980b9',
                waterColor: '#85c1e9',
                sensorColor: '#e74c3c',
                textColor: '#2c3e50'
              }
            }
          }
        },
        {
          id: 'condenser25d',
          name: '2.5D凝汽器',
          icon: 'icon-condenser-3d',
          type: 'condenser25d',
          preview: '/src/assets/component-previews/condenser-25d.svg',
          defaultProps: {
            width: 240,
            height: 140,
            data: {
              static: {
                isRunning: true,
                pressure: 7.2,
                temperature: 42,
                efficiency: 94,
                vacuumLevel: 96,
                flowRate: 2800,
                shellColor: '#5dade2',
                pipeColor: '#3498db',
                lightPipeColor: '#85c1e9',
                darkPipeColor: '#2980b9',
                waterPipeColor: '#16a085',
                lightWaterPipeColor: '#48c9b0',
                waterColor: '#7fb3d3',
                pumpColor: '#34495e',
                lightPumpColor: '#5d6d7e',
                sensorColor: '#e74c3c',
                flowSensorColor: '#9b59b6',
                textColor: '#2c3e50'
              }
            }
          }
        }
      ]
    }
  ])

  // 获取当前选中的组件
  const selectedComponents = computed(() => {
    if (!currentProject.value || editorState.value.selectedComponents.length === 0) {
      return []
    }
    
    return currentProject.value.components.filter(component => 
      editorState.value.selectedComponents.includes(component.id)
    )
  })

  // 更新性能指标
  const updatePerformanceMetrics = throttle(() => {
    performanceMetrics.value = {
      componentCount: currentProject.value?.components.length || 0,
      connectionCount: currentProject.value?.connections.length || 0,
      renderTime: 0,
      memoryUsage: (performance as any).memory?.usedJSHeapSize || 0,
      lastUpdateTime: Date.now()
    }
  }, 1000)

  // 定期清理缓存
  let cacheCleanupTimer: number | null = null
  
  const startCacheCleanup = () => {
    if (cacheCleanupTimer) clearInterval(cacheCleanupTimer)
    
    cacheCleanupTimer = setInterval(() => {
      // 清理过期的缓存
      cleanupExpiredCaches()
      
      // 强制垃圾回收（如果可用）
      if ((globalThis as any).gc) {
        (globalThis as any).gc()
      }
    }, CACHE_CLEANUP_INTERVAL)
  }

  const cleanupExpiredCaches = () => {
    // 这里可以清理各种缓存，比如组件渲染缓存等
    console.log('执行缓存清理...')
  }

  // 添加新项目
  function addProject(project: ConfigurationProject) {
    projectList.value.push(project)
    updatePerformanceMetrics()
  }

  // 更新项目
  function updateProject(project: ConfigurationProject) {
    const index = projectList.value.findIndex(p => p.id === project.id)
    if (index !== -1) {
      projectList.value[index] = project
      updatePerformanceMetrics()
    }
  }

  // 删除项目
  function deleteProject(id: string) {
    const index = projectList.value.findIndex(p => p.id === id)
    if (index !== -1) {
      projectList.value.splice(index, 1)
      updatePerformanceMetrics()
    }
  }

  // 初始化项目数据绑定
  function initializeProjectDataBindings(project: ConfigurationProject) {
    if (!project.dataBindings) {
      project.dataBindings = [
        {
          id: 'tank-levels',
          name: '储罐液位监测',
          type: 'api',
          config: {
            url: '/api/tank-levels',
            method: 'GET',
            interval: 2000
          },
          fields: [
            { key: 'tank-001-level', name: '1号储罐液位', type: 'number', unit: 'L' },
            { key: 'tank-002-level', name: '2号储罐液位', type: 'number', unit: 'L' },
            { key: 'tank-003-level', name: '3号储罐液位', type: 'number', unit: 'L' }
          ]
        }
      ]
    }
  }

  // 设置当前项目
  function setCurrentProject(project: ConfigurationProject) {
    // 初始化数据绑定
    initializeProjectDataBindings(project)

    currentProject.value = project
    // 初始化历史记录
    editorState.value.history.present = JSON.parse(JSON.stringify(project))
    editorState.value.history.past = []
    editorState.value.history.future = []
    // 清空选中状态
    editorState.value.selectedComponents = []
    
    updatePerformanceMetrics()
    startCacheCleanup()
  }

  // 防抖的组件更新
  const debouncedUpdateComponent = debounce((component: ConfigurationComponent) => {
    if (!currentProject.value) return
    
    const index = currentProject.value.components.findIndex(c => c.id === component.id)
    if (index !== -1) {
      currentProject.value.components[index] = component
      updatePerformanceMetrics()
    }
  }, 100)

  // 添加组件到当前项目
  function addComponent(component: ConfigurationComponent) {
    if (!currentProject.value) return
    
    currentProject.value.components.push(component)
    // 记录历史
    debouncedSaveHistory()
    // 选中新添加的组件
    editorState.value.selectedComponents = [component.id]
    updatePerformanceMetrics()
  }

  // 更新组件
  function updateComponent(component: ConfigurationComponent) {
    if (!currentProject.value) return
    
    const index = currentProject.value.components.findIndex(c => c.id === component.id)
    if (index !== -1) {
      currentProject.value.components[index] = component
      // 记录历史
      debouncedSaveHistory()
      updatePerformanceMetrics()
    }
  }

  // 批量更新组件（性能优化）
  function updateComponents(components: ConfigurationComponent[]) {
    if (!currentProject.value) return
    
    components.forEach(component => {
      const index = currentProject.value!.components.findIndex(c => c.id === component.id)
      if (index !== -1) {
        currentProject.value!.components[index] = component
      }
    })
    
    debouncedSaveHistory()
    updatePerformanceMetrics()
  }

  // 删除组件
  function deleteComponent(id: string) {
    if (!currentProject.value) return
    
    const index = currentProject.value.components.findIndex(c => c.id === id)
    if (index !== -1) {
      currentProject.value.components.splice(index, 1)
      // 记录历史
      saveHistory()
      // 清除选中状态
      editorState.value.selectedComponents = editorState.value.selectedComponents.filter(cid => cid !== id)
      updatePerformanceMetrics()
    }
  }

  // 选择组件
  function selectComponent(id: string, multiple = false) {
    if (!multiple) {
      editorState.value.selectedComponents = [id]
    } else {
      if (!editorState.value.selectedComponents.includes(id)) {
        editorState.value.selectedComponents.push(id)
      }
    }
  }

  // 取消选择组件
  function deselectComponent(id: string) {
    editorState.value.selectedComponents = editorState.value.selectedComponents.filter(cid => cid !== id)
  }

  // 清空选择
  function clearSelection() {
    editorState.value.selectedComponents = []
  }

  // 复制选中组件
  function copySelectedComponents() {
    if (!currentProject.value || editorState.value.selectedComponents.length === 0) return
    
    const components = currentProject.value.components.filter(component => 
      editorState.value.selectedComponents.includes(component.id)
    )
    
    // 深拷贝，避免引用问题
    editorState.value.clipboard = JSON.parse(JSON.stringify(components))
  }

  // 粘贴组件
  function pasteComponents() {
    if (!currentProject.value || editorState.value.clipboard.length === 0) return
    
    const newComponents = JSON.parse(JSON.stringify(editorState.value.clipboard))
    const newIds: string[] = []
    
    // 为粘贴的组件生成新ID并偏移位置
    newComponents.forEach(component => {
      const oldId = component.id
      component.id = generateUniqueId()
      component.x += 20
      component.y += 20
      newIds.push(component.id)
    })
    
    // 添加到当前项目
    currentProject.value.components.push(...newComponents)
    
    // 选中新粘贴的组件
    editorState.value.selectedComponents = newIds
    
    // 记录历史
    saveHistory()
    updatePerformanceMetrics()
  }

  // 防抖的历史记录保存
  const debouncedSaveHistory = debounce(() => {
    saveHistory()
  }, 500)

  // 保存历史记录（优化版本）
  function saveHistory() {
    if (!currentProject.value) return
    
    try {
      // 添加当前状态到历史记录
      editorState.value.history.past.push(JSON.parse(JSON.stringify(editorState.value.history.present)))
      // 更新当前状态
      editorState.value.history.present = JSON.parse(JSON.stringify(currentProject.value))
      // 清空未来状态
      editorState.value.history.future = []
      
      // 限制历史记录数量，防止内存溢出
      if (editorState.value.history.past.length > HISTORY_LIMIT) {
        // 删除最老的记录
        editorState.value.history.past.splice(0, editorState.value.history.past.length - HISTORY_LIMIT)
      }
    } catch (error) {
      console.error('保存历史记录失败:', error)
    }
  }

  // 撤销
  function undo() {
    if (!currentProject.value || editorState.value.history.past.length === 0) return
    
    try {
      // 保存当前状态到未来记录
      editorState.value.history.future.unshift(JSON.parse(JSON.stringify(editorState.value.history.present)))
      // 获取上一个状态
      const previousState = editorState.value.history.past.pop()
      // 更新当前状态
      editorState.value.history.present = previousState as ConfigurationProject
      // 更新当前项目
      currentProject.value = JSON.parse(JSON.stringify(previousState))
      updatePerformanceMetrics()
    } catch (error) {
      console.error('撤销操作失败:', error)
    }
  }

  // 重做
  function redo() {
    if (!currentProject.value || editorState.value.history.future.length === 0) return
    
    try {
      // 保存当前状态到历史记录
      editorState.value.history.past.push(JSON.parse(JSON.stringify(editorState.value.history.present)))
      // 获取下一个状态
      const nextState = editorState.value.history.future.shift()
      // 更新当前状态
      editorState.value.history.present = nextState as ConfigurationProject
      // 更新当前项目
      currentProject.value = JSON.parse(JSON.stringify(nextState))
      updatePerformanceMetrics()
    } catch (error) {
      console.error('重做操作失败:', error)
    }
  }

  // 生成唯一ID
  function generateUniqueId() {
    return 'component_' + Date.now() + '_' + Math.floor(Math.random() * 1000)
  }

  // 清理资源
  function cleanup() {
    if (cacheCleanupTimer) {
      clearInterval(cacheCleanupTimer)
      cacheCleanupTimer = null
    }
  }

  return {
    projectList,
    currentProject,
    editorState,
    componentLibrary,
    selectedComponents,
    performanceMetrics,
    addProject,
    updateProject,
    deleteProject,
    setCurrentProject,
    addComponent,
    updateComponent,
    updateComponents,
    deleteComponent,
    selectComponent,
    deselectComponent,
    clearSelection,
    copySelectedComponents,
    pasteComponents,
    saveHistory,
    undo,
    redo,
    cleanup
  }
})