<template>
  <!-- 这里使用一个全宽高的容器作为 ECharts 绘图区域 -->
  <div ref="chartRef" style="width: 100%; height: 100%;"></div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onUnmounted } from 'vue'
import * as echarts from 'echarts'

// 定义传入图表的数据类型
interface DataItem {
  label: string
  value: number
}
interface TimeDataItem {
  time: string
  datas: DataItem[]
}

const props = defineProps<{
  chartData: TimeDataItem[]
}>()

const chartRef = ref<HTMLDivElement | null>(null)
let chartInstance: echarts.ECharts | null = null
let resizeObserver: ResizeObserver | null = null

function initChart() {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value)
    updateChart()
  }
}
function hexToRgba(hex: string, alpha: number): string {
  hex = hex.replace(/^#/, '')
  if (hex.length === 3) {
    hex = hex.split('').map(c => c + c).join('')
  }
  const bigint = parseInt(hex, 16)
  const r = (bigint >> 16) & 255
  const g = (bigint >> 8) & 255
  const b = bigint & 255
  return `rgba(${r}, ${g}, ${b}, ${alpha})`
}
watch(
  () => props.chartData,
  (newData) => {
    nextTick(() => {
      if (!newData || newData.length === 0) {
        chartInstance?.clear()
      } else {
        updateChart()
      }
    })
  },
  { deep: true }
)
function updateChart() {
  if (!chartInstance) return

  // 提取 x 轴数据（时间）
  const timePoints = props.chartData.map(item => {

    return item.time && item.time.length >= 16 ? item.time.substring(11, 16) : item.time
  })

  // 提取图例名称，假设所有时间点的 datas 结构一致
  let legends: string[] = []
  if (props.chartData.length > 0 && props.chartData[0].datas) {
    legends = props.chartData[0].datas.map((d) => d.label)
  }

  const defaultColors = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc']


  // 构造 series 数据
  const series = legends.map((label, i) => {
    const baseColor = defaultColors[i % defaultColors.length]
    return {
      name: label,
      type: 'line',
      smooth: true,
      lineStyle: { color: baseColor },
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: hexToRgba(baseColor, 0.6) },
          { offset: 1, color: hexToRgba(baseColor, 0) }
        ])
      },
      data: props.chartData.map(item => {
        // 找到当前点对应的记录
        const found = item.datas.find(d => d.label === label)
        // 返回对象，注意每个数据点的单位可能不同
        return found ? { value: found.value, myUnit: found.unit || '' } : { value: null, myUnit: '' }
      })
    }
  })

  const option = {
    tooltip: {
      trigger: 'axis',
      formatter: function (params: any) {
        let html = params[0].axisValue + '<br/>'
        params.forEach((item: any) => {
          // 如果 item.data 是个对象，则读取其 value 与 myUnit
          let dataObj = item.data
          let value = dataObj
          let unit = ''
          if (typeof dataObj === 'object' && dataObj !== null) {
            value = dataObj.value
            unit = dataObj.myUnit || ''
          }
          html += `${item.marker}${item.seriesName}: ${value}${unit ? ' ' + unit : ''}<br/>`
        })
        return html
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      top: '10%',
      bottom: '10%',
    },
    legend: {
      data: legends,
      textStyle: {
        color: '#ffffff'
      },
      icon: 'circle'
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: timePoints,
      axisLabel: { color: '#fff' }, // 设置刻度标签颜色
    },
    yAxis: {
      type: 'value',
      axisLabel: { show: true, color: '#fff' }, // 设置刻度标签颜色
      splitLine: {
        show: true,
        lineStyle: { type: 'dashed', color: '#3f4e61' },
      },
    },
    series: series,
  }

  chartInstance.setOption(option)
}
function resizeHandler() {
  if (chartInstance) {
    chartInstance.resize()
  }
}
onMounted(() => {
  initChart()
  resizeObserver = new ResizeObserver(() => {
    chartInstance?.resize()
  })
  if (chartRef.value) {
    resizeObserver.observe(chartRef.value)
  }
})

// 当图表数据发生变化时更新配置
watch(
  () => props.chartData,
  () => {
    updateChart()
  },
  { deep: true }
)

onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose()
  }
  resizeObserver && resizeObserver.disconnect()
})
</script>

<style scoped>
/* 可根据需要添加样式 */
</style>
