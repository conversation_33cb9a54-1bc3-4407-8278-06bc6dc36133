<template>
  <div class="container1" :style="{ height: containerHeight }">
    <div class="header" v-show="isFullscreen">CIMS智慧监控管理平台</div>
    <div class="content" :style="{ height: contentHeight }">
      <!-- 左侧 -->
      <div class="left">
        <!-- 运行概览 -->
        <div class="left-top">
          <div class="top-title">
            <div class="cardHeader">
              <span class="title">
                {{
                    screenTitleData.length > 0 && screenTitleData[0].name
                      ? screenTitleData[0].name
                      : '今日运行概览'
                }}
              </span>
              <span
                class="arrowFlex"
                @click="() => screenTitleData.length > 0 && screenTitleData[0].configDetailId && handleTitleClick(screenTitleData[0].configDetailId,screenTitleData[0].remark)"
              ></span>
            </div>
            <div class="cardBody">
              <div class="cardBodySegmented">
                <!-- 运行概览机组切换 -->
                <el-segmented v-model="overviewUnitValue" :options="overviewUnitOptions" v-if="overviewUnitOptions.length > 1" />
              </div>

              <!-- 根据 selectedOverviewData 展示对应机组的数据 -->
              <div class="cardBodyContent" v-if="selectedOverviewData && selectedOverviewData.datas">
                <div class="contentDivBig" :style="{ gap: contentDivBigGap }">
                  <!-- 负荷 load -->
                  <div class="bigDiv">
                    <img src="@/assets/images/fhicon.png" alt="" style="width: 100%; height: 100%" />
                    <div class="bigDivContent">
                      <div class="bigDivContent-title">
                        {{ getOverviewItem(0).label || '负荷' }}
                      </div>
                      <div class="bigDivContent-content">
                        <span class="bigDivContent-number" :style="{ color: getNumberColor(getOverviewItem(0).status) }">
                          {{ getOverviewItem(0).value ?? '--' }}
                        </span>
                        <span class="bigDivContent-unit"> /&nbsp;{{ getOverviewItem(0).unit ?? '' }} </span>
                      </div>
                    </div>
                  </div>

                  <!-- 真空 vacuum_value -->
                  <div class="bigDiv">
                    <img src="@/assets/images/zkicon.png" alt="" style="width: 100%; height: 100%" />
                    <div class="bigDivContent">
                      <div class="bigDivContent-title">
                        {{ getOverviewItem(1).label || '真空' }}
                      </div>
                      <div class="bigDivContent-content">
                        <span class="bigDivContent-number" :style="{ color: getNumberColor(getOverviewItem(1).status) }">
                          {{ getOverviewItem(1).value ?? '--' }}
                        </span>
                        <span class="bigDivContent-unit"> /&nbsp;{{ getOverviewItem(1).unit ?? '' }} </span>
                      </div>
                    </div>
                  </div>

                  <!-- 端差 ttd -->
                  <div class="bigDiv">
                    <img src="@/assets/images/dcicon.png" alt="" style="width: 100%; height: 100%" />
                    <div class="bigDivContent">
                      <div class="bigDivContent-title">
                        {{ getOverviewItem(2).label || '端差' }}
                      </div>
                      <div class="bigDivContent-content">
                        <span class="bigDivContent-number" :style="{ color: getNumberColor(getOverviewItem(2).status) }">
                          {{ getOverviewItem(2).value ?? '--' }}
                        </span>
                        <span class="bigDivContent-unit"> /&nbsp;{{ getOverviewItem(2).unit ?? '' }} </span>
                      </div>
                    </div>
                  </div>

                  <!-- 温升 temperature_rise -->
                  <div class="bigDiv">
                    <img src="@/assets/images/wsicon.png" alt="" style="width: 100%; height: 100%" />
                    <div class="bigDivContent">
                      <div class="bigDivContent-title">
                        {{ getOverviewItem(3).label || '温升' }}
                      </div>
                      <div class="bigDivContent-content">
                        <span class="bigDivContent-number" :style="{ color: getNumberColor(getOverviewItem(3).status) }">
                          {{ getOverviewItem(3).value ?? '--' }}
                        </span>
                        <span class="bigDivContent-unit"> /&nbsp;{{ getOverviewItem(3).unit ?? '' }} </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 水质分析 -->
        <div class="left-center">
          <div class="top-title">
            <div class="cardHeader">
              <span class="title">
                {{
                    screenTitleData.length > 1 && screenTitleData[1].name
                      ? screenTitleData[1].name
                      : '水质分析'
                }}
              </span>
              <span
                class="arrowFlex"
                @click="() => screenTitleData.length > 1 && screenTitleData[1].configDetailId && handleTitleClick(screenTitleData[1].configDetailId,screenTitleData[1].remark)"
              ></span>
            </div>
          </div>
          <div class="center-cardBody">
            <div class="cardBodySegmented">
              <!-- 水质分析机组切换 -->
              <el-segmented v-model="waterQualityUnitValue" :options="overviewUnitOptions" v-if="overviewUnitOptions.length > 1" />
            </div>
            <div class="chart-container">
              <!-- 这里是水质分析的表格或图表 -->
              <elephantColumnChart :chartData="selectedWaterQualityData" />
            </div>
          </div>
        </div>

        <!-- 冷却系统概览 -->
        <div class="left-bottom">
          <div class="top-title">
            <div class="cardHeader">
              <span class="title">
                {{
                    screenTitleData.length > 2 && screenTitleData[2].name
                      ? screenTitleData[2].name
                      : '冷却系统概览'
                }}
              </span>
              <div class="cardHeaderRight">
                <!-- 冷却系统机组切换 -->
                <el-segmented v-model="coolingUnitValue" :options="overviewUnitOptions" v-if="overviewUnitOptions.length > 1" />
              </div>
              <!-- <span
                class="arrowFlex"
                @click="() => screenTitleData.length > 2 && screenTitleData[2].configDetailId && handleTitleClick(screenTitleData[2].configDetailId,screenTitleData[2].remark)"
              >
            </span> -->
            </div>
          </div>
          <div class="bottom-cardBody">
            <!-- 冷却塔效率 -->
            <div class="bottom-list">
              <div class="bottom-list-img">
                <img src="@/assets/images/lqticon.png" alt="" style="width: 100%; height: 100%" />
              </div>
              <div class="bottom-list-content">
                <div class="characters">
                  <span style="color:#fff; font-size:14px;">
                    {{ getCoolingItem(0).label || '冷却塔效率' }}
                  </span>
                  <span style="color:#fff; font-size:16px; transform: skew(-10deg);">
                    {{ getCoolingItem(0).value ?? '--' }}
                    {{ getCoolingItem(0).unit || '' }}
                  </span>
                </div>
                <div class="picture">
                  <div class="line"></div>

                  <!-- 计算渲染信息 -->
                  <template v-if="coolEffInfo = getCoolingRenderInfo(getCoolingItem(0))" />

                  <!-- 左端点 -->
                  <div class="point left-point" v-if="coolEffInfo.showLeft" :style="{ left: coolEffInfo.leftPos }">
                    <span class="point-text">{{ coolEffInfo.leftText }}</span>
                  </div>

                  <!-- 右端点 -->
                  <div class="point right-point" v-if="coolEffInfo.showRight" :style="{ left: coolEffInfo.rightPos }">
                    <span class="point-text">{{ coolEffInfo.rightText }}</span>
                  </div>

                  <!-- 移动点 -->
                  <div class="point moving-point" v-if="coolEffInfo.showMoving" :style="{ left: coolEffInfo.movingPos }"></div>
                </div>
              </div>
            </div>

            <!-- 湿球温度 -->
            <div class="bottom-list">
              <div class="bottom-list-img">
                <img src="@/assets/images/sqwdicon.png" alt="" style="width: 100%; height: 100%" />
              </div>
              <div class="bottom-list-content">
                <div class="characters">
                  <span style="color:#fff; font-size:14px;">
                    {{ getCoolingItem(1).label || '湿球温度' }}
                  </span>
                  <span style="color:#fff; font-size:16px; transform: skew(-10deg);">
                    {{ getCoolingItem(1).value ?? '--' }}
                    {{ getCoolingItem(1).unit || '' }}
                  </span>
                </div>
                <div class="picture">
                  <div class="line"></div>

                  <!-- 计算渲染信息 -->
                  <template v-if="wetInfo = getCoolingRenderInfo(getCoolingItem(1))" />

                  <div class="point left-point" v-if="wetInfo.showLeft" :style="{ left: wetInfo.leftPos }">
                    <span class="point-text">{{ wetInfo.leftText }}</span>
                  </div>
                  <div class="point right-point" v-if="wetInfo.showRight" :style="{ left: wetInfo.rightPos }">
                    <span class="point-text">{{ wetInfo.rightText }}</span>
                  </div>
                  <div class="point moving-point" v-if="wetInfo.showMoving" :style="{ left: wetInfo.movingPos }"></div>
                </div>
              </div>
            </div>

            <!-- 逼近度 -->
            <div class="bottom-list">
              <div class="bottom-list-img">
                <img src="@/assets/images/bjdicon.png" alt="" style="width: 100%; height: 100%" />
              </div>
              <div class="bottom-list-content">
                <div class="characters">
                  <span style="color:#fff; font-size:14px;">
                    {{ getCoolingItem(2).label || '逼近度' }}
                  </span>
                  <span style="color:#fff; font-size:16px; transform: skew(-10deg);">
                    {{ getCoolingItem(2).value ?? '--' }}
                    {{ getCoolingItem(2).unit || '' }}
                  </span>
                </div>
                <div class="picture">
                  <div class="line"></div>

                  <!-- 计算渲染信息 -->
                  <template v-if="bjdInfo = getCoolingRenderInfo(getCoolingItem(2))" />

                  <div class="point left-point" v-if="bjdInfo.showLeft" :style="{ left: bjdInfo.leftPos }">
                    <span class="point-text">{{ bjdInfo.leftText }}</span>
                  </div>
                  <div class="point right-point" v-if="bjdInfo.showRight" :style="{ left: bjdInfo.rightPos }">
                    <span class="point-text">{{ bjdInfo.rightText }}</span>
                  </div>
                  <div class="point moving-point" v-if="bjdInfo.showMoving" :style="{ left: bjdInfo.movingPos }"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间（数据趋势总览） -->
      <div class="center" :style="{ height: `${centerHeight}px` }">
        <div class="top-title">
          <div class="cardHeader">
            <span class="title">
              {{
                  screenTitleData.length > 3 && screenTitleData[3].name
                    ? screenTitleData[3].name
                    : '数据趋势总览'
              }}
              <div>
                <el-segmented v-model="TrendUnitValue" :options="TrendUnitOptions" v-if="TrendUnitOptions.length > 1" />
              </div>
              <div>
                <el-select v-model="TrendSubSystemValue" placeholder="请选择子系统" @change="handleTrendSubSystemChange" style="width: 200px;">
                  <el-option v-for="option in TrendSubSystemOptions" :key="option.value" :label="option.label" :value="option.value"> </el-option>
                </el-select>
              </div>
            </span>

            <!-- <span
              class="arrowFlex"
              v-if="screenTitleData.length > 3 && screenTitleData[3].configDetailId"
              @click="() => handleTitleClick(screenTitleData[3].configDetailId,screenTitleData[3].remark)"
            ></span> -->
          </div>
        </div>
        <div class="cardBody">
          <!-- <div class="cardBodySegmented"></div> -->
          <div class="cardBodyContent" style="height: 100%;">
            <trendAreaChart :chartData="currentChartData" />
          </div>
        </div>
      </div>

      <!-- 右侧 -->
      <div class="right">
        <!-- 循泵概览 -->
        <div class="right-top">
          <div class="top-title">
            <div class="cardHeader">
              <span class="title">
                {{
                    screenTitleData.length > 4 && screenTitleData[4].name
                      ? screenTitleData[4].name
                      : '循泵概览'
                }}
              </span>
              <span
                class="arrowFlex"
                @click="() => screenTitleData.length > 4 && screenTitleData[4].configDetailId && handleTitleClick(screenTitleData[4].configDetailId,screenTitleData[4].remark)"
              ></span>
            </div>
            <div class="cardBody">
              <div class="cardBodySegmented">
                <!-- 循泵机组切换 -->
                <el-segmented v-model="pumpValue" :options="overviewUnitOptions" v-if="overviewUnitOptions.length > 1" />
              </div>

              <!-- 循泵概览数据展示 -->
              <div class="cardBodyContent" v-if="selectedPumpData && selectedPumpData.datas">
                <div class="contentDivBig" :style="{ gap: rightcontentDivBigGap }">
                  <!-- 循泵效率 circle_water_pump_eff -->
                  <div class="contentDiv">
                    <img src="@/assets/images/xlicon.png" alt="" style="width: 100%; height: 100%" />
                    <div class="bigDivContent">
                      <div class="bigDivContent-number">
                        {{ getPumpItem(0).value ?? '--' }}
                      </div>
                      <div class="bigDivContent-content">
                        <span class="bigDivContent-title"> {{ getPumpItem(0).label || '--' }}&nbsp; </span>
                        <span class="bigDivContent-unit"> &nbsp;{{ getPumpItem(0).unit ?? '' }} </span>
                      </div>
                    </div>
                  </div>

                  <!-- 循环水量 circle_water_flow -->
                  <div class="contentDiv">
                    <img src="@/assets/images/slicon.png" alt="" style="width: 100%; height: 100%" />
                    <div class="bigDivContent">
                      <div class="bigDivContent-number">
                        {{ getPumpItem(1).value ?? '--' }}
                      </div>
                      <div class="bigDivContent-content">
                        <span class="bigDivContent-title"> {{ getPumpItem(1).label || '--' }}&nbsp; </span>
                        <span class="bigDivContent-unit"> &nbsp;{{ getPumpItem(1).unit ?? '' }} </span>
                      </div>
                    </div>
                  </div>

                  <!-- 循环倍率 circle_ratio -->
                  <div class="contentDiv">
                    <img src="@/assets/images/blicon.png" alt="" style="width: 100%; height: 100%" />
                    <div class="bigDivContent">
                      <div class="bigDivContent-number">
                        {{ getPumpItem(2).value ?? '--' }}
                      </div>
                      <div class="bigDivContent-content">
                        <span class="bigDivContent-title"> {{ getPumpItem(2).label || '--' }}&nbsp; </span>
                        <span class="bigDivContent-unit"> &nbsp;{{ getPumpItem(2).unit ?? '' }} </span>
                      </div>
                    </div>
                  </div>

                  <!-- 凝结水流量 condensed_quantity -->
                  <div class="contentDiv">
                    <img src="@/assets/images/sllicon.png" alt="" style="width: 100%; height: 100%" />
                    <div class="bigDivContent">
                      <div class="bigDivContent-number">
                        {{ getPumpItem(3).value ?? '--' }}
                      </div>
                      <div class="bigDivContent-content">
                        <span class="bigDivContent-title"> {{ getPumpItem(3).label || '--' }}&nbsp; </span>
                        <span class="bigDivContent-unit"> &nbsp;{{ getPumpItem(3).unit ?? '' }} </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 没有数据时也可以做个占位 -->
            </div>
          </div>
        </div>

        <!-- 告警统计 -->
        <div class="right-center">
          <div class="top-title">
            <div class="cardHeader">
              <span class="title">
                {{
                    screenTitleData.length > 5 && screenTitleData[5].name
                      ? screenTitleData[5].name
                      : '告警统计'
                }}
              </span>
              <span
                class="arrowFlex"
                @click="() => screenTitleData.length > 5 && handleTitleClick(screenTitleData[5].configDetailId,screenTitleData[5].remark)"
              ></span>
            </div>
          </div>
          <div class="right-cardBody">
            <div class="chartBox">
              <CircularChart :alarmData="alarmCountData" />
            </div>
            <ul class="innerCardBox">
              <li class="cardItem">
                <div class="innerCardTitle">告警处理率</div>
                <main class="innerCardContent">{{ alarmCountData.dealRate?alarmCountData.dealRate:'--' }}%</main>
              </li>
              <li class="cardItem">
                <div class="innerCardTitle">未处理告警数</div>
                <main class="innerCardContent">{{ alarmCountData.notDealCount?alarmCountData.notDealCount:'--' }}</main>
              </li>
              <li class="cardItem">
                <div class="innerCardTitle">近七日告警数</div>
                <main class="innerCardContent">{{ alarmCountData.recentCount?alarmCountData.recentCount:'--' }}</main>
              </li>
            </ul>
          </div>
        </div>

        <!-- 智能运维 -->
        <div class="right-bottom">
          <div class="top-title">
            <div class="cardHeader">
              <span class="title">
                {{
                    screenTitleData.length > 6 && screenTitleData[6].name
                      ? screenTitleData[6].name
                      : '智能运维'
                }}
              </span>
              <span
                class="arrowFlex"
                @click="() => screenTitleData.length > 6 && screenTitleData[6].configDetailId && handleTitleClick(screenTitleData[6].configDetailId,screenTitleData[6].remark)"
              ></span>
            </div>
          </div>
          <div class="bottom-cardBody">
            <div class="cardBodySegmented" v-if="showIntelligentSegmented">
              <el-segmented v-model="intelligentValue" :options="overviewUnitOptions" v-if="overviewUnitOptions.length>1" />
            </div>
            <div class="bottom-cardBodyContent">
              <div class="currentRun">
                <div class="current-title">
                  <div class="current-title-text">
                    <span><img src="@/assets/images/currentRun.png" alt="" /></span>
                    <span style="line-height: 23px;">当前运行</span>
                  </div>
                  <div class="current-title-img">
                    <img src="@/assets/images/divider.png" alt="" />
                  </div>
                </div>
                <div class="currentRun-content">
                  <div class="currentRun-content-item narrow-fan">
                    <img :src="getFanImage(currentFan?.runStatus)" alt="fan" />
                    <div class="currentRuntext">
                      <div class="currentRuntext-title">风机</div>
                      <div class="currentRuntext-number" :style="{ color: getRunStatusColor(currentFan?.runStatus) }">
                        {{ currentFan?.value||'--' }}&nbsp;{{ currentFan?.unit || '' }}
                      </div>
                    </div>
                  </div>
                  <div class="currentRun-content-item">
                    <img :src="getPumpImage(currentPump?.runStatus)" alt="pump" />
                    <div class="currentRuntext">
                      <div class="currentRuntext-title">水泵</div>
                      <div class="currentRuntext-number" :style="{ color: getRunStatusColor(currentFan?.runStatus) }">
                        {{ currentPump?.value ?? '--' }}&nbsp;{{ currentPump?.unit || '' }}
                      </div>
                    </div>
                  </div>
                  <!-- <div class="currentRun-content-item"></div> -->
                </div>
              </div>
              <div class="suggestRun">
                <div class="current-title">
                  <div class="current-title-text">
                    <span><img src="@/assets/images/currentRun.png" alt="" /></span>
                    <span style="line-height: 23px;">建议运行</span>
                  </div>
                  <div class="current-title-img">
                    <!-- <span style="line-height: 23px;"></span> -->
                    <div
                      style="display: flex;height: 100%;width: 100%;align-items: center;justify-content: center;font-size: 14px;color: transparent;
                      background: -webkit-gradient(linear, left top, left bottom, from(#fff), color-stop(50%, #b3c7ff));
                      background: -webkit-linear-gradient(top, #fff, #b3c7ff 50%);
                      background: linear-gradient(180deg, #fff, #b3c7ff 50%);
                      -webkit-background-clip: text;
                      background-clip: text;"
                    >
                      {{recommendContent }}
                    </div>
                  </div>
                </div>
                <div class="currentRun-content">
                  <div class="currentRun-content-item">
                    <img :src="getFanImage(recommendFan?.runStatus)" alt="fan" />
                    <div class="currentRuntext">
                      <div class="currentRuntext-title">风机</div>
                      <div class="currentRuntext-number" :style="{ color: getRunStatusColor(recommendFan?.runStatus) }">
                        <!-- {{ recommendFan?.value ? recommendFan.value + (recommendFan.unit || '') : '--' }} -->
                        {{ recommendFan?.value||'--' }}&nbsp;{{ recommendFan?.unit || '' }}
                      </div>
                    </div>
                  </div>
                  <div class="currentRun-content-item">
                    <img :src="getPumpImage(recommendPump?.runStatus)" alt="pump" />
                    <div class="currentRuntext">
                      <div class="currentRuntext-title">水泵</div>
                      <div class="currentRuntext-number" :style="{ color: getRunStatusColor(recommendFan?.runStatus) }">
                        {{ recommendPump?.value ?? '--' }}{{ recommendPump?.unit || '' }}
                      </div>
                    </div>
                  </div>
                  <div class="currentRun-content-item">
                    <img :src="getPowerGenImage(realImprovePower?.runStatus)" alt="power" />
                    <div class="currentRuntext">
                      <div class="currentRuntext-title">预增发电量</div>
                      <div class="currentRuntext-number" :style="{ color: getRunStatusColor(recommendFan?.runStatus) }">
                        {{ realImprovePower?.value ? realImprovePower.value + (realImprovePower.unit || '') : '--' }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- right end -->
    </div>
    <!-- content end -->

    <div class="float-btn" @click="handleToggle">
      <svg-icon :icon-class="isFullscreen ? 'exit-fullscreen' : 'fullscreen'" />
      <span style="display: flex">{{ isFullscreen ? '退出' : '全屏' }}</span>
    </div>
    <div class="circle-btn" @click="circleBtn" v-if="!isFullscreen">
      <span style="display: flex">返回</span>
    </div>
  </div>
  <!-- 弹框 -->
  <el-dialog v-model="visible" :title="visibletitle" :width="dialogwidth" center :before-close="closeDialog">
    <component :is="dynamicComponent" v-if="dynamicComponent" v-bind="dynamicProps" />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useFullscreen } from '@vueuse/core'
import useAppStore from '@/store/modules/app'
import { useRouter } from 'vue-router'
import elephantColumnChart from '@/components/elephantColumnChart/index.vue'
import CircularChart from '@/components/CircularChart/index.vue'
import trendAreaChart from './AreaLineChart/index.vue'
import {
  getScreenTitle,
  getScreenContent,
  getAlarmCount,
  getSmartOperation,
  getRunOverview,
  getWaterQuality,
  getDataTrend,
  getCoolingSystem,
  getLoopPump,
  getAlarmCountPopup,
  getSmartOperationPopup,
} from './index.api'
import alertStat from './screenDialog/alertStat.vue'
import circlePump from './screenDialog/circlePump.vue'
import coolSystem from './screenDialog/coolSystem.vue'
import intelligent from './screenDialog/intelligent.vue'
import runOverview from './screenDialog/runOverview.vue'
import waterAnalysis from './screenDialog/waterAnalysis.vue'

//
import waterPump0 from '@/assets/images/waterpump0.png'
import fan0 from '@/assets/images/fan0.png'
import waterPump1 from '@/assets/images/waterpump1.png'
import fan1 from '@/assets/images/fan1.png'
import powerGeneration1 from '@/assets/images/powerGeneration1.png'
import waterPump2 from '@/assets/images/waterpump2.png'
import fan2 from '@/assets/images/fan2.png'
import powerGeneration2 from '@/assets/images/powerGeneration2.png'
import waterPump3 from '@/assets/images/waterpump3.png'
import fan3 from '@/assets/images/fan3.png'
import powerGeneration3 from '@/assets/images/powerGeneration3.png'

import emitter from '@/utils/eventBus.js'
import { useLocalCache, CACHE_KEY } from '@/hooks/web/useCache'

const dialogwidth = ref('800')
const visible = ref(false)
const visibletitle = ref('')
const dynamicComponent = ref<any>(null) // 用于记录要渲染哪个子组件
const dynamicProps = ref<any>({}) // 用于给子组件传递参数
const remarkComponentMap: Record<string, any> = {
  runOverview,
  waterAnalysis,
  coolSystem,
  circlePump,
  alertStat,
  intelligent,
}
const { wsCache } = useLocalCache()
const cachedProjects = wsCache.get(CACHE_KEY.projectList)
const { id: projectId } = cachedProjects || { id: '' }

// 如果有项目切换逻辑，这里保持原写法
emitter.on('projectListChanged', (e) => {
  location.reload()
})
// 水质分析数据
const screenWaterQualityData = ref<any>({})
const selectedWaterQualityData = computed(() => {
  const dataArr = screenWaterQualityData.value?.data || []
  const selectedData = dataArr.find((item: any) => item.powerUnitId === waterQualityUnitValue.value) || null
  return selectedData?.datas || []
})

// 帮助函数：通过 identifier 找到对应的数据
// function getCoolingItem(identifier: string) {
//   return screenCoolingSystemData.value.find((i) => i.identifier === identifier) || {}
// }
function getCoolingItem(index: number) {
  if (!selectedCoolingData.value || !selectedCoolingData.value.length) {
    return {}
  }
  // 确保索引在合法范围内
  return selectedCoolingData.value[index] || {}
}
/**
 * 入口函数：根据 dataItem 的 min、max、value 判定使用哪种规则：
 * 1. min、max 都无 => 三点全隐藏
 * 2. 只有 max => [0..max] => [0%..50%]
 * 3. 只有 min => [min..0] => [50%..100%]
 * 4. 同时有 min、max => 使用"三段区间"，并且 min 在 25%，max 在 75%。
 */
function getCoolingRenderInfo(item: any) {
  if (!item) return hiddenAll()

  const { min, max, value } = item

  // 情况1: min、max 都无 => 全隐藏
  if (min == null && max == null) {
    return hiddenAll()
  }

  // 情况2: 只有 max
  if (min == null && max != null) {
    return calcOnlyMax(max, value)
  }

  // 情况3: 只有 min
  if (min != null && max == null) {
    return calcOnlyMin(min, value)
  }

  // 情况4: 同时有 min、max => 用你要的"三段区间"，并且左右点分别显示在 25%、75%
  return calcThreeSegmentWithPointsAt25_75(min, max, value)
}

/** 三点都隐藏 */
function hiddenAll() {
  return {
    showLeft: false,
    leftPos: '0%',
    leftText: '',
    showRight: false,
    rightPos: '100%',
    rightText: '',
    showMoving: false,
    movingPos: '0%',
  }
}

/** 只有 max => [0..max] => [0%..50%]。只显示右端点(50%)，左端点隐藏 */
function calcOnlyMax(maxVal: number, val: number | null) {
  const showMoving = val != null
  let movingPos = '0%'
  if (showMoving) {
    // 超出范围贴边
    const safeVal = Math.max(0, Math.min(val!, maxVal))
    // [0..maxVal] => [0..50%]
    const ratio = safeVal / maxVal
    movingPos = (ratio * 50).toFixed(2) + '%'
  }
  return {
    showLeft: false,
    leftPos: '0%',
    leftText: '',
    showRight: true,
    rightPos: '50%',
    rightText: maxVal,
    showMoving,
    movingPos,
  }
}

/** 只有 min => [min..0] => [50%..100%]。只显示左端点(50%)，右端点隐藏 */
function calcOnlyMin(minVal: number, val: number | null) {
  const showMoving = val != null
  let movingPos = '50%'
  if (showMoving) {
    const safeVal = Math.max(minVal, Math.min(val!, 0))
    // ratio in [0..1], => [50%..100%]
    const ratio = (safeVal - minVal) / (0 - minVal)
    movingPos = (50 + ratio * 50).toFixed(2) + '%'
  }
  return {
    showLeft: true,
    leftPos: '50%',
    leftText: minVal,
    showRight: false,
    rightPos: '100%',
    rightText: '',
    showMoving,
    movingPos,
  }
}

/**
 * min、max 都有 => 你的"三段区间"并且 **min 显示在25%**，**max 显示在75%**。
 *
 * 1) 先计算区间:
 *    delta = max-min
 *    extMin = min - delta
 *    extMax = max + delta
 * 2) 将 [extMin..min] 映射到 [0..25%],
 *           [min..max] 映射到 [25%..75%],
 *           [max..extMax] 映射到 [75%..100%].
 * 3) 左端点放在 25%, 右端点放在 75%.
 * 4) 若 value==null => 移动点不显示
 */
function calcThreeSegmentWithPointsAt25_75(minVal: number, maxVal: number, val: number | null) {
  // 如果没有value，就隐藏移动点，只显示端点
  if (val == null) {
    return {
      showLeft: true,
      leftPos: '25%',
      leftText: minVal,
      showRight: true,
      rightPos: '75%',
      rightText: maxVal,
      showMoving: false,
      movingPos: '0%',
    }
  }

  const delta = maxVal - minVal
  const extMin = minVal - delta
  const extMax = maxVal + delta

  // 把 value 限制在 [extMin..extMax]
  const safeVal = Math.max(extMin, Math.min(val, extMax))

  let posPercent = 0
  if (safeVal <= minVal) {
    // 落在 [extMin..min] => [0..25%]
    const ratio = (safeVal - extMin) / (minVal - extMin)
    posPercent = ratio * 25
  } else if (safeVal <= maxVal) {
    // 落在 [min..max] => [25%..75%]
    const ratio = (safeVal - minVal) / (maxVal - minVal)
    posPercent = 25 + ratio * 50
  } else {
    // 落在 [max..extMax] => [75%..100%]
    const ratio = (safeVal - maxVal) / (extMax - maxVal)
    posPercent = 75 + ratio * 25
  }

  // 限制到 [0..100]
  posPercent = Math.min(Math.max(posPercent, 0), 100)
  const movingPos = posPercent.toFixed(2) + '%'

  return {
    showLeft: true,
    leftPos: '25%', // min 点始终在25%
    leftText: minVal,
    showRight: true,
    rightPos: '75%', // max 点始终在75%
    rightText: maxVal,
    showMoving: true,
    movingPos,
  }
}

// segmented 选中的值
const overviewUnitValue = ref<number | string>() // 运行概览机组切换
const pumpValue = ref<number | string>() // 循泵机组切换
const intelligentValue = ref<number | string>() // 智能运维机组切换
const waterQualityUnitValue = ref<number | string>() // 水质分析机组切换
const coolingUnitValue = ref<number | string>() // 冷却系统机组切换
const overviewUnitOptions = ref<any[]>([]) // 存储机组选项
const TrendUnitValue = ref<number | string>() // 机组趋势切换
const TrendUnitOptions = ref<any[]>([]) // 存储趋势机组选项
// 当前选择子系统（下拉选择器）的值以及对应选项
const TrendSubSystemValue = ref<number | string>()
const TrendSubSystemOptions = ref<any[]>([])

const isHeader = ref(false)
const router = useRouter()
const { isFullscreen, toggle } = useFullscreen()
const appStore = useAppStore()

const containerHeight = computed(() => {
  return appStore.sidebar.topHide ? '100vh' : 'calc(100vh - 85px)'
})
const centerHeight = computed(() => {
  return appStore.sidebar.topHide ? '320' : '260'
})
const contentHeight = computed(() => {
  return appStore.sidebar.topHide ? 'calc(100vh - 85px)' : 'calc(100vh - 95px)'
})
const contentDivBigGap = computed(() => {
  return appStore.sidebar.topHide ? '48px 2px' : '28px 2px'
})
const rightcontentDivBigGap = computed(() => {
  return appStore.sidebar.topHide ? '68px 2px' : '30px 2px'
})

watch(isFullscreen, (val) => {
  appStore.setTopHide(val)
})

// 移除了 watch(overviewUnitOptions) 监听器，因为现在在数据加载完成后手动设置默认值

// 进入界面时点击全屏
const handleToggle = () => {
  isHeader.value = !isHeader.value
  toggle()
}
// 返回上一级
const circleBtn = () => {
  // 返回到/index
  router.push('/')
}
// 关闭弹窗
const closeDialog = () => {
  // console.log(1112222)
  visible.value = false
  dynamicComponent.value = null
  dynamicProps.value = {}
  visibletitle.value = '' // 清空或默认
}
// 点击查询大屏每个模块详情数据
async function handleTitleClick(configDetailId: number | string, remark: string) {
  // console.log('点击的 configDetailId:', configDetailId, ' remark:', remark)
  // 1) 清空上次残留
  dynamicComponent.value = null
  dynamicProps.value = {}
  visibletitle.value = '' // 清空或默认

  try {
    let fetchData: any = null

    switch (remark) {
      case 'runOverview': {
        // 调用运行概览接口
        const res = await getRunOverview({ configId: configDetailId })
        // 拿到数据
        fetchData = res.data
        // 赋值给 dynamicProps
        dynamicProps.value = { dialogData: fetchData }
        // 显示哪个组件
        dynamicComponent.value = remarkComponentMap.runOverview
        visibletitle.value = '运行概览详情'
        dialogwidth.value = '1200px'
        break
      }

      case 'waterAnalysis': {
        // 调用水质分析接口
        const res = await getWaterQuality({ configId: configDetailId })
        fetchData = res.data
        dynamicProps.value = { dialogData: fetchData }
        dynamicComponent.value = remarkComponentMap.waterAnalysis
        visibletitle.value = '水质分析详情'
        dialogwidth.value = '700px'
        break
      }

      case 'coolSystem': {
        // 冷却系统接口
        // const res = await getCoolingSystem({ configId: configDetailId })
        // fetchData = res.data
        // dynamicProps.value = {dialogData:fetchData}
        // dynamicComponent.value = remarkComponentMap.coolSystem
        // visibletitle.value = '冷却系统概览详情'
        break
      }

      case 'circlePump': {
        // 循泵概览接口
        const res = await getLoopPump({ configId: configDetailId })
        fetchData = res.data
        // console.log(fetchData, 'fetchData')
        dynamicProps.value = { dialogData: fetchData }
        dynamicComponent.value = remarkComponentMap.circlePump
        visibletitle.value = '循泵概览详情'
        dialogwidth.value = '800px'
        break
      }
      case 'alertStat': {
        // 告警统计接口
        // const res = await getAlarmCountPopup({ configId: configDetailId })
        fetchData = []
        dynamicProps.value = { dialogData: fetchData }
        dynamicComponent.value = remarkComponentMap.alertStat
        visibletitle.value = '告警统计详情'
        dialogwidth.value = '1200px'
        break
      }

      case 'intelligent': {
        // 智能运维接口
        const res = await getSmartOperationPopup({ configId: configDetailId })
        fetchData = res.data
        dynamicProps.value = { dialogData: fetchData }
        dynamicComponent.value = remarkComponentMap.intelligent
        visibletitle.value = '智能运维详情'
        break
      }

      default:
        // 不识别的 remark 处理
        console.warn('无效的 remark:', remark)
        break
    }

    // 3) 弹窗显示
    visible.value = true
  } catch (err) {
    console.error('handleTitleClick 出错:', err)
    // 也可以在此做错误提示，或保底逻辑
  }
}

// 大屏初始数据
const screenTitleData = ref<any[]>([])
// 运行概览数据
const screenOverviewData = ref<any>({})
// 循泵概览
const screenPumpData = ref<any>({})
// 冷却系统概览
const screenCoolingSystemData = ref<any>({})
// 告警统计
const alarmCountData = ref<any>({})
// 智能运维概览
const screenIntelligentData = ref<any>([])
const getDataTrendData = ref<any[]>([])

// 根据 coolingUnitValue 找到对应机组的「冷却系统」数据
const selectedCoolingData = computed(() => {
  const dataArr = screenCoolingSystemData.value?.data || []
  const selectedData = dataArr.find((item: any) => item.powerUnitId === coolingUnitValue.value) || null
  return selectedData?.datas || []
})

// 获取标题接口
const getScreenTitleData = async () => {
  const params = {
    projectId,
    configType: 9,
  }
  try {
    const res = await getScreenTitle(params)
    if (res.data.rows && res.data.rows.length > 0) {
      // 机组下拉选项
      overviewUnitOptions.value = res.data.rows[0].powerUnits.map((option: any) => ({
        label: option.name,
        value: option.id, // 注意这里的 id 是否是 number，必要时可转 Number(option.id)
      }))

      // 排序
      screenTitleData.value = res.data.rows.sort((a: any, b: any) => a.orderNum - b.orderNum)

      // 创建一个数据加载完成的计数器
      let loadedCount = 0
      const totalModules = screenTitleData.value.length

      // 设置默认值的函数
      const setDefaultValues = () => {
        if (overviewUnitOptions.value.length > 0 && !overviewUnitValue.value) {
          const defaultOption = overviewUnitOptions.value[0]
          overviewUnitValue.value = defaultOption.value
          pumpValue.value = defaultOption.value
          intelligentValue.value = defaultOption.value
          waterQualityUnitValue.value = defaultOption.value
          coolingUnitValue.value = defaultOption.value
        }
      }

      // 检查是否所有模块数据都已加载
      const checkAllLoaded = () => {
        loadedCount++
        if (loadedCount >= totalModules) {
          // 所有数据加载完成后设置默认值
          setDefaultValues()
        }
      }

      // 根据模块顺序获取对应模块数据
      screenTitleData.value.forEach((item, index) => {
        // index 为 0 -> 运行概览
        if (index === 0) {
          getScreenContentData(item.id).then((resContent) => {
            if (resContent.data && resContent.data.length > 0) {
              screenOverviewData.value = resContent
            } else {
              screenOverviewData.value = {}
            }
            checkAllLoaded()
          }).catch(() => {
            screenOverviewData.value = {}
            checkAllLoaded()
          })
        }
        // index = 1 -> 水质分析
        else if (index === 1) {
          getScreenContentData(item.id).then((resContent) => {
            // 存储完整的数据结构以支持机组切换
            if (resContent.data && resContent.data.length > 0) {
              screenWaterQualityData.value = resContent
            } else {
              screenWaterQualityData.value = {}
            }
            checkAllLoaded()
          }).catch(() => {
            screenWaterQualityData.value = {}
            checkAllLoaded()
          })
        }
        // index = 2 -> 冷却系统概览
        else if (index === 2) {
          getScreenContentData(item.id).then((resContent) => {
            if (resContent.data && resContent.data.length > 0) {
              screenCoolingSystemData.value = resContent
            } else {
              screenCoolingSystemData.value = {}
            }
            checkAllLoaded()
          }).catch(() => {
            screenCoolingSystemData.value = {}
            checkAllLoaded()
          })
        }
        // index = 4 -> 循泵概览
        else if (index === 4) {
          getScreenContentData(item.id).then((resContent) => {
            if (resContent.data && resContent.data.length > 0) {
              screenPumpData.value = resContent
              // console.log(screenPumpData.value, '这是screenPumpData.value')
            } else {
              screenPumpData.value = {}
            }
            checkAllLoaded()
          }).catch(() => {
            screenPumpData.value = {}
            checkAllLoaded()
          })
        }
        else if (index === 5) {
          getAlarmCountData()
          checkAllLoaded()
        }
        else if (index === 6) {
          getSmartOperationData(item.id).then((resContent) => {
            if (resContent.data && resContent.data.length > 0) {
              screenIntelligentData.value = resContent.data
            }
            checkAllLoaded()
          }).catch(() => {
            screenIntelligentData.value = []
            checkAllLoaded()
          })
        }
        else if (index === 3) {
          getDataTrendList(item.id).then((resContent) => {
            if (resContent.data && resContent.data.length > 0) {
              getDataTrendData.value = resContent.data
            }
            checkAllLoaded()
          }).catch(() => {
            getDataTrendData.value = []
            checkAllLoaded()
          })
        }
        else {
          // 对于其他未处理的index，也要调用checkAllLoaded
          checkAllLoaded()
        }
      })
    } else {
      screenTitleData.value = []
    }
  } catch (err) {
    console.error(err)
    screenTitleData.value = []
  }
}
// 计算当前选中的趋势单元数据
const selectedTrendUnitData = computed(() => {
  return getDataTrendData.value?.find((unit: any) => unit.powerUnitId === TrendUnitValue.value)
})
// 根据当前选中的子系统索引获取图表数据
const currentChartData = computed(() => {
  if (selectedTrendUnitData.value && selectedTrendUnitData.value.subSystem && typeof TrendSubSystemValue.value === 'number') {
    const data = selectedTrendUnitData.value.subSystem[TrendSubSystemValue.value].timeDataList
    // 如果 timeDataList 为空，则返回空数组
    return data && data.length > 0 ? data : []
  }
  return []
})

// 监听 getDataTrendData 数据加载完成后，初始化趋势单元选项
watch(
  getDataTrendData,
  (newVal) => {
    if (newVal && newVal.length > 0) {
      // 构建趋势单元选项
      TrendUnitOptions.value = newVal.map((unit: any) => ({
        label: unit.powerUnitName,
        value: unit.powerUnitId,
      }))
      // 默认选中第一个
      if (!TrendUnitValue.value) {
        TrendUnitValue.value = newVal[0].powerUnitId
      }
    }
  },
  { immediate: true }
)
// 监听趋势单元切换，更新下拉选择器对应的子系统选项
watch(TrendUnitValue, (newVal) => {
  if (newVal && selectedTrendUnitData.value && selectedTrendUnitData.value.subSystem) {
    // 构造下拉选项：这里用数组索引做 value，label 为子系统名称
    TrendSubSystemOptions.value = selectedTrendUnitData.value.subSystem.map((sub: any, index: number) => ({
      label: sub.name,
      value: index,
    }))
    // 默认选中第一个子系统
    TrendSubSystemValue.value = 0
    // 同时打印默认子系统对应的图表数据
    handleTrendSubSystemChange(0)
  } else {
    TrendSubSystemOptions.value = []
    TrendSubSystemValue.value = undefined
  }
})
// 当下拉选择器值改变时，打印出对应的 timeDataList 数据
function handleTrendSubSystemChange(val: number | string) {
  // 使用当前趋势单元数据和子系统 index 从中获取 timeDataList 数据
  if (selectedTrendUnitData.value && selectedTrendUnitData.value.subSystem && selectedTrendUnitData.value.subSystem[val]) {
    const chartData = selectedTrendUnitData.value.subSystem[val].timeDataList
    // console.log('选中的图表数据：', chartData)
  } else {
    console.log('当前子系统没有图表数据')
  }
}
// 查询标题下面的内容数据
const getScreenContentData = async (configId: any) => {
  return getScreenContent({ configId })
}
// 查询数据趋势纵览数据
const getDataTrendList = async (configId: any) => {
  return getDataTrend({ configId })
}
// 查询告警统计数据
const getAlarmCountData = async () => {
  getAlarmCount({ projectId })
    .then((res) => {
      alarmCountData.value = res.data
    })
    .catch((err) => {
      alarmCountData.value = {}
    })
}
// 查询智能运维数据
const getSmartOperationData = async (configId: any) => {
  return getSmartOperation({ configId })
}
// 定时器
let intervalId: number
let titleIntervalId: number
// 数据趋势总览定时器
let trendUnitTimer: number | null = null
let subSystemTimer: number | null = null
// 自动轮播的延时设置
const trendUnitDelay = 5000 // 一个趋势单元轮播完毕后，等待 5 秒再切换到下一个趋势单元
const subSystemDelay = 5000 // 每个子系统显示 5 秒
// 当前正在轮播的趋势单元的索引
let currentTrendUnitIndex = 0
// 当前正在轮播的子系统的索引
let currentSubSystemIndex = 0
// 自动滚动：先更新趋势单元，再开始子系统轮播
function autoScrollTrendUnit() {
  if (trendUnitTimer) {
    clearTimeout(trendUnitTimer)
    trendUnitTimer = null
  }
  if (subSystemTimer) {
    clearTimeout(subSystemTimer)
    subSystemTimer = null
  }
  if (TrendUnitOptions.value.length > 0) {
    // 获取当前趋势单元选项
    const currentTrendOption = TrendUnitOptions.value[currentTrendUnitIndex]
    TrendUnitValue.value = currentTrendOption.value

    // 延时500ms再启动子系统自动滚动
    setTimeout(() => {
      currentSubSystemIndex = 0
      autoScrollSubSystem()
    }, 500)
  }
}
// 自动轮播当前趋势单元内的子系统
function autoScrollSubSystem() {
  if (subSystemTimer) {
    clearTimeout(subSystemTimer)
    subSystemTimer = null
  }
  const subOptions = TrendSubSystemOptions.value
  if (subOptions && subOptions.length > 0) {
    TrendSubSystemValue.value = subOptions[currentSubSystemIndex].value
    // 调用处理当前子系统对应的图表数据
    handleTrendSubSystemChange(currentSubSystemIndex)

    currentSubSystemIndex++
    if (currentSubSystemIndex < subOptions.length) {
      subSystemTimer = window.setTimeout(autoScrollSubSystem, subSystemDelay)
    } else {
      if (TrendUnitOptions.value.length === 1) {
        // 如果只有一个趋势单元，重新循环该趋势单元的子系统
        currentSubSystemIndex = 0
        subSystemTimer = window.setTimeout(autoScrollSubSystem, subSystemDelay)
      } else {
        trendUnitTimer = window.setTimeout(() => {
          currentTrendUnitIndex = (currentTrendUnitIndex + 1) % TrendUnitOptions.value.length
          autoScrollTrendUnit()
        }, trendUnitDelay)
      }
    }
  } else {
    if (TrendUnitOptions.value.length === 1) {
      subSystemTimer = window.setTimeout(autoScrollSubSystem, subSystemDelay)
    } else {
      trendUnitTimer = window.setTimeout(() => {
        currentTrendUnitIndex = (currentTrendUnitIndex + 1) % TrendUnitOptions.value.length
        autoScrollTrendUnit()
      }, trendUnitDelay)
    }
  }
}

watch(getDataTrendData, (newVal) => {
  if (trendUnitTimer) {
    clearTimeout(trendUnitTimer)
    trendUnitTimer = null
  }
  if (subSystemTimer) {
    clearTimeout(subSystemTimer)
    subSystemTimer = null
  }
  if (newVal && newVal.length > 0) {
    // 数据加载完毕后再启动滚动
    autoScrollTrendUnit()
  }
})

onMounted(() => {
  getScreenTitleData()
  // 自动轮播
  intervalId = window.setInterval(() => {
    if (overviewUnitOptions.value.length <= 0) return
    const currentIndex = overviewUnitOptions.value.findIndex((option) => option.value === overviewUnitValue.value)
    // 下一个 index
    const nextIndex = (currentIndex + 1) % overviewUnitOptions.value.length
    const nextOption = overviewUnitOptions.value[nextIndex]
    // 同步更新所有模块的机组值
    overviewUnitValue.value = nextOption.value
    pumpValue.value = nextOption.value
    waterQualityUnitValue.value = nextOption.value
    coolingUnitValue.value = nextOption.value
    // 智能运维只有在不是多机组数据时才更新
    if (!hasMultipleUnits.value) {
      intelligentValue.value = nextOption.value
    }
  }, 10000)
  titleIntervalId = window.setInterval(() => {
    getScreenTitleData()
  }, 60000)
})

onUnmounted(() => {
  clearInterval(intervalId)
  clearInterval(titleIntervalId)
  if (trendUnitTimer) window.clearTimeout(trendUnitTimer)
  if (subSystemTimer) window.clearTimeout(subSystemTimer)
})

// 根据 overviewUnitValue 找到对应机组的「运行概览」数据
const selectedOverviewData = computed(() => {
  const dataArr = screenOverviewData.value?.data || []
  // 匹配 powerUnitId
  return dataArr.find((item: any) => item.powerUnitId === overviewUnitValue.value) || null
})

// 根据 pumpValue 找到对应机组的「循泵概览」数据
const selectedPumpData = computed(() => {
  const dataArr = screenPumpData.value?.data || []
  return dataArr.find((item: any) => item.powerUnitId === pumpValue.value) || null
})
const showIntelligentSegmented = computed(() => {
  // 如果数据中存在 powerUnitId 包含逗号（多机组）的项，则隐藏
  const dataArr = screenIntelligentData.value || []
  return !dataArr.some((item: any) => item.powerUnitId.includes(','))
})
// 用来判断"是否存在多机组ID的数据"
const hasMultipleUnits = computed(() => {
  const dataArr = screenIntelligentData.value || []
  // 如果 powerUnitId 里包含逗号，就说明是多个机组ID合并
  return dataArr.some((item: any) => item.powerUnitId.includes(','))
})

// 根据intelligentValue找到对应机组的「智能运维」数据
const selectedIntelligentData = computed(() => {
  const dataArr = screenIntelligentData.value || []
  // 先找有没有多机组ID的数据
  const multipleIdItem = dataArr.find((item: any) => item.powerUnitId.includes(','))
  if (multipleIdItem) {
    // 如果有，则直接返回这条数据
    return multipleIdItem
  }
  // 否则就按原先逻辑，匹配单个机组 ID
  return dataArr.find((item: any) => item.powerUnitId === String(intelligentValue.value)) || null
})

const recommendContent = computed(() => {
  if (selectedIntelligentData.value && selectedIntelligentData.value.recommend) {
    try {
      // 尝试解析 recommend 字符串
      const parsed = JSON.parse(selectedIntelligentData.value.recommend)
      // 假设 parsed 是个数组，这里可根据需求展示第一个，或者拼接所有元素
      return parsed.join(' ') // 也可只返回 parsed[0]，例如：return parsed[0] || '--'
    } catch (error) {
      console.error('解析 recommend 失败：', error)
      return selectedIntelligentData.value.recommend
    }
  }
  return '--'
})

// 当前运行列表
const currentFan = computed(() => {
  return selectedIntelligentData.value?.currentList.find((i: any) => i.code === 'currentFan') || null
})
const currentPump = computed(() => {
  return selectedIntelligentData.value?.currentList.find((i: any) => i.code === 'currentPump') || null
})
// 建议运行列表
const recommendFan = computed(() => {
  return selectedIntelligentData.value?.suggestList.find((i: any) => i.code === 'recommendFan') || null
})
const recommendPump = computed(() => {
  return selectedIntelligentData.value?.suggestList.find((i: any) => i.code === 'recommendPump') || null
})
const realImprovePower = computed(() => {
  return selectedIntelligentData.value?.suggestList.find((i: any) => i.code === 'realImprovePower') || null
})
const getRunStatusColor = (runStatus?: number) => {
  switch (runStatus) {
    case 0:
      return '#A4CFFF'
    case 1:
      return '#00CC88'
    case 2:
      return '#FFBB00'
    case 3:
      return '#DD2C00'
    default:
      // 默认颜色随意
      return '#ffffff'
  }
}
const getFanImage = (runStatus: number) => {
  switch (runStatus) {
    case 0:
      return fan0
    case 1:
      return fan1
    case 2:
      return fan2
    case 3:
      return fan3
    case 4:
      return fan1
    default:
      return fan0
  }
}
// 根据 runStatus 返回不同 waterPump 图片
const getPumpImage = (runStatus: number) => {
  switch (runStatus) {
    case 0:
      return waterPump0
    case 1:
      return waterPump1
    case 2:
      return waterPump2
    case 3:
      return waterPump3
    case 4:
      return waterPump1
    default:
      return waterPump3
  }
}
// 根据 runStatus 返回不同 powerGeneration 图片
const getPowerGenImage = (runStatus: number) => {
  switch (runStatus) {
    case 1:
      return powerGeneration1
    case 2:
      return powerGeneration2
    case 3:
      return powerGeneration3
    default:
      return powerGeneration3
  }
}
function getNumberColor(status: number | undefined) {
  switch (status) {
    case 0:
      return 'rgba(153, 197, 255, 1)'
    case 1:
      return 'rgba(221, 44, 0, 1)'
    case 2:
      return 'rgba(255, 141, 26, 1)'
    default:
      return 'rgba(153, 197, 255, 1)'
  }
}
// 帮助函数：根据 identifier 在 selectedOverviewData.datas 中找到对应项
function getOverviewItem(index: number) {
  if (!selectedOverviewData.value || !selectedOverviewData.value.datas) {
    return {}
  }
  return selectedOverviewData.value.datas[index] || {}
}

// 帮助函数：根据 identifier 在 selectedPumpData.datas 中找到对应项
// function getPumpItem(identifier: string) {
//   if (!selectedPumpData.value || !selectedPumpData.value.datas) {
//     return {}
//   }
//   return selectedPumpData.value.datas.find((i: any) => i.identifier === identifier) || {}
// }
function getPumpItem(index: number) {
  if (!selectedPumpData.value || !selectedPumpData.value.datas) {
    return {}
  }
  // 确保索引在合法范围内
  return selectedPumpData.value.datas[index] || {}
}
</script>

<style scoped>
:deep(.el-select__wrapper) {
  background: linear-gradient(0deg, rgba(0, 111, 255, 0.1), rgba(0, 111, 255, 0.1)), rgba(24, 37, 61, 0.5);
  box-shadow: none !important;
  border: 1px solid #006fff;
  font-size: 16px;
}
:deep(.el-select__placeholder) {
  color: #e6e6e6;
}
</style>
<style lang="scss" scoped>
.custom-style .el-segmented {
  --el-segmented-item-selected-color: var(--el-text-color-primary);
  --el-segmented-item-selected-bg-color: #ffd100;
  --el-border-radius-base: 16px;
}
.container1 {
  padding: 5px 0;
  // background-image: url('@/assets/images/bigbg2.png');
  width: 100%;
  background-size: 1920px 1080px;
  .header {
    height: 80px;
    width: 100%;
    font-size: 27px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #b4d0fc;
    line-height: 69px;
    letter-spacing: 1px;
    text-align: center;
    background-image: url('@/assets/images/header.png');
  }
  .content {
    display: grid;
    grid-template-columns: 432px 1fr 463px;
    align-items: flex-end;
    overflow: hidden;
    position: relative;
    gap: 20px;

    .left,
    .right {
      display: grid;
      grid-template-rows: repeat(3, 1fr);
      height: 100%;
      overflow: hidden;
      gap: 10px;
      .left-top,
      .left-center,
      .left-bottom,
      .right-top,
      .right-center,
      .right-bottom {
        height: 100%;
        background: url('@/assets/images/Moduleframe.png') no-repeat center/100% 100%;
        .top-title {
          background: url('@/assets/images/title.png') no-repeat 0 0 / contain;
          height: 36px;
          position: relative;
          border-bottom: none;
          box-sizing: border-box;
          .cardHeader {
            display: flex;
            align-items: center;
            font-family: PingFangSC-Medium, PingFang SC;
            padding: 6px 0 0 40px;
            .title {
              height: 22px;
              color: transparent;
              line-height: 22px;
              font-size: 18px;
              background: -webkit-gradient(linear, left top, left bottom, from(#fff), color-stop(50%, #73d0ff));
              background: -webkit-linear-gradient(top, #fff, #73d0ff 50%);
              background: linear-gradient(176deg, #fff, #73d0ff 50%);
              -webkit-background-clip: text;
              background-clip: text;
              font-weight: 600;
            }
            .cardHeaderRight {
              margin-left: auto;
              margin-right: 5px;
            }
            .arrowFlex {
              cursor: pointer;
              margin-left: auto;
              width: 28px;
              height: 28px;
              background-image: url('@/assets/images/arrow.png');
              position: absolute;
              top: 0;
              right: 12px;
            }
          }
        }
      }
    }

    .left {
      .left-top {
        .top-title {
          .cardBody {
            .cardBodySegmented {
              text-align: center;
              padding: 15px 0;
            }
            .cardBodyContent {
              display: flex;
              height: 100%;
              justify-content: center;
              padding: 20px 0;
              box-sizing: border-box;
              .contentDivBig {
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                width: 100%;
                overflow-y: auto;
                overflow-x: hidden;
                justify-items: center;
                gap: 20px;
                .bigDiv {
                  display: grid;
                  grid-template-columns: 52px 1fr;
                  column-gap: 15px;
                  border-radius: 4px;
                  font-family: PingFangSC-Medium, PingFang SC;
                  .bigDivContent {
                    .bigDivContent-title {
                      color: #fff;
                      font-size: 14px;
                      font-weight: 400;
                    }
                    .bigDivContent-content {
                      width: 115px;
                      padding: 3px 10px 3px 0;
                      background: url('@/assets/images/dwbg.png');
                      background-size: 100% 100%;
                      color: rgba(153, 197, 255, 1);
                      display: flex;
                      justify-content: space-between;
                      .bigDivContent-unit {
                        font-size: 10px;
                        margin-top: 10px;
                        transform: skew(-10deg);
                      }
                      .bigDivContent-number {
                        font-size: 18px;
                        font-weight: 400;
                        line-height: 24px;
                        transform: skew(-10deg);
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
      .left-center {
        .center-cardBody {
          height: calc(100% - 36px);
          display: flex;
          flex-direction: column;
          .cardBodySegmented {
            text-align: center;
            padding: 10px 0;
            flex-shrink: 0;
          }
          .chart-container {
            flex: 1;
            overflow: hidden;
          }
        }
      }
      .left-bottom {
        .bottom-cardBody {
          height: calc(100% - 36px);
          display: grid;
          grid-template-rows: repeat(3, 1fr);
          gap: 5px;
          padding: 20px 0;
          .bottom-list {
            display: grid;
            padding: 0 10px 0 20px;
            gap: 10px;
            grid-template-columns: 50px 1fr;
            align-items: center;
            .bottom-list-img {
              text-align: center;
            }
            .bottom-list-content {
              .characters {
                display: flex;
                justify-content: space-between;
                span {
                  padding: 2px 10px;
                }
              }
              .picture {
                position: relative;
                width: 100%;
                height: 20px;
                .line {
                  position: absolute;
                  top: 50%;
                  left: 0;
                  right: 0;
                  height: 6px;
                  background: rgba(0, 213, 255, 1);
                  box-shadow: 0px 0px 8px rgba(51, 221, 255, 1);
                  transform: translateY(-50%);
                  border-radius: 5px;
                }
                .point {
                  position: absolute;
                  top: 50%;
                  width: 14px;
                  height: 14px;
                  border-radius: 50%;
                  background: rgba(42, 130, 228, 1);
                  transform: translate(-50%, -50%);
                  .point-text {
                    position: absolute;
                    top: 20px; // 控制距离圆点的垂直距离
                    left: 50%;
                    transform: translateX(-50%);
                    font-size: 12px;
                    color: #fff; // 你想要的颜色
                    white-space: nowrap; // 如果数字较多，可以保证不换行
                  }
                }
                .moving-point {
                  border: 2px solid rgba(255, 255, 255, 1);
                }
                .left-point {
                  left: 25%;
                }
                .right-point {
                  left: 75%;
                }
              }
            }
          }
        }
      }
    }
    .center {
      height: 260px;
      z-index: 2;
      position: relative;
      background: url('@/assets/images/Emoduleframe.png') no-repeat center/100% 100%;
      .top-title {
        background: url('@/assets/images/title.png') no-repeat 0 0 / contain;
        height: 36px;
        position: relative;
        border-bottom: none;
        box-sizing: border-box;
        .cardHeader {
          display: flex;
          align-items: center;
          font-family: PingFangSC-Medium, PingFang SC;
          padding: 3px 0 0 40px;
          .title {
            height: 22px;
            color: transparent;
            line-height: 22px;
            font-size: 18px;
            background: -webkit-gradient(linear, left top, left bottom, from(#fff), color-stop(50%, #73d0ff));
            background: -webkit-linear-gradient(top, #fff, #73d0ff 50%);
            background: linear-gradient(176deg, #fff, #73d0ff 50%);
            -webkit-background-clip: text;
            background-clip: text;
            font-weight: 600;
            width: 100%;
            display: grid;
            grid-template-columns: repeat(3, 1fr);
          }
          //
          // .arrowFlex {
          //   cursor: pointer;
          //   margin-left: auto;
          //   width: 28px;
          //   height: 28px;
          //   background-image: url('@/assets/images/arrow.png');
          //   position: absolute;
          //   top: 0;
          //   right: 12px;
          // }
        }
      }
      .cardBody {
        height: calc(100% - 36px);
        .cardBodyContent {
          height: 100%;
        }
      }
    }
    .right {
      .right-top {
        .top-title {
          .cardBody {
            .cardBodySegmented {
              text-align: center;
              padding: 15px 0;
            }
            .cardBodyContent {
              display: flex;
              height: 100%;
              justify-content: center;
              padding: 20px 0;
              box-sizing: border-box;
              .contentDivBig {
                display: grid;
                grid-template-columns: repeat(2, 5fr);
                width: 100%;
                overflow-y: auto;
                overflow-x: hidden;
                justify-items: center;
                .contentDiv {
                  width: 180px;
                  height: 52px;
                  display: grid;
                  grid-template-columns: 52px 1fr;
                  column-gap: 15px;
                  border-radius: 4px;
                  font-family: PingFangSC-Medium, PingFang SC;
                  background: url('@/assets/images/glbg.png') no-repeat center/100% 100%;
                  .bigDivContent {
                    .bigDivContent-number {
                      font-size: 20px;
                      font-weight: 400;
                      letter-spacing: 1px;
                      line-height: 24px;
                      color: rgba(153, 197, 255, 1);
                      transform: skew(-10deg);
                    }
                    .bigDivContent-content {
                      .bigDivContent-unit {
                        font-size: 12px;
                        font-weight: 400;
                        line-height: 17.38px;
                        color: rgba(204, 204, 204, 1);
                        transform: skew(-10deg);
                      }
                      .bigDivContent-title {
                        font-size: 14px;
                        font-weight: 400;
                        color: rgba(255, 255, 255, 1);
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
      .right-center {
        .right-cardBody {
          height: calc(100% - 36px);
          display: flex;
          .chartBox {
            flex: 1;
            width: 100%;
            height: 100%;
          }
          .innerCardBox {
            padding: 15px !important;
            list-style: none;
            flex: 1;
            display: grid;
            gap: 12px;
            grid-column-end: span 2;
            height: calc(100% - 20px);
            overflow: auto;
            .cardItem {
              height: 100%;
              display: flex;
              justify-content: space-between;
              align-items: center;
              border-radius: 8px;
              box-sizing: border-box;
              padding: 0 8px;
              background: url('@/assets/images/alarmbg.png') no-repeat center/100% 100%;
              .innerCardTitle {
                font-family: PingFangSC, PingFang SC;
                font-weight: 400;
                font-size: 14px;
                color: #b4d0fc;
                line-height: 16px;
              }
              .innerCardContent {
                font-weight: 600;
                font-size: 20px;
                color: #4ddefc;
                line-height: 28px;
                transform: skew(-10deg);
              }
            }
          }
        }
      }
      .right-bottom {
        .bottom-cardBody {
          height: calc(100% - 36px);
          .cardBodySegmented {
            text-align: center;
            // padding: 15px 0 5px 0px;
          }
          .bottom-cardBodyContent {
            height: calc(100% - 58px);
            display: flex;
            flex-direction: column;
            padding: 0 5px;
            .currentRun,
            .suggestRun {
              flex: 1;
              .current-title {
                display: grid;
                gap: 10px;
                grid-template-columns: 100px 1fr;
                height: 33px;
                .current-title-text {
                  display: flex;
                  justify-content: space-evenly;
                  font-weight: 500;

                  line-height: 18px;
                  font-size: 14px;
                  color: transparent;
                  background: -webkit-gradient(linear, left top, left bottom, from(#fff), color-stop(50%, #b3c7ff));
                  background: -webkit-linear-gradient(top, #fff, #b3c7ff 50%);
                  background: linear-gradient(180deg, #fff, #b3c7ff 50%);
                  -webkit-background-clip: text;
                  background-clip: text;
                }
                .current-title-img {
                  line-height: 18px;
                }
              }
              .currentRun-content {
                display: grid;
                grid-template-columns: 110px 1fr 150px;
                // gap: 18px 2px;
                height: calc(100% - 25px);
                .currentRun-content-item {
                  display: grid;
                  grid-template-columns: 52px 1fr;
                  // column-gap: 15px;
                  border-radius: 4px;
                  font-family: PingFangSC-Medium, PingFang SC;
                  .currentRuntext {
                    .currentRuntext-title {
                      font-size: 14px;
                      font-weight: 400;
                      letter-spacing: 1px;
                      line-height: 20.27px;
                      color: rgba(230, 230, 230, 1);
                    }
                    .currentRuntext-number {
                      font-size: 14px;
                      font-weight: 400;
                      line-height: 24px;
                      word-break: break-all;
                    }
                  }
                }
              }
            }
            .suggestRun {
              .current-title {
                .current-title-img {
                  padding: 0 8px;
                  background: url('@/assets/images/suggestionbg.png') no-repeat center/100% 100%;
                }
              }
            }
          }
        }
      }
    }
  }
  .float-btn {
    z-index: 2;
    position: fixed;
    top: 300px;
    right: 0;
    padding: 10px 0 0 7px;
    width: 32px;
    height: 81px;
    border-radius: 10px 0 0 10px;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    text-align: center;
    color: #abd1ff;
    background: rgba(0, 43, 137, 0.8);
    cursor: pointer;
  }
  .circle-btn {
    z-index: 2;
    position: fixed;
    top: 120px; /* 修改这里 */
    right: 0;
    padding: 10px 0 0 7px;
    width: 32px;
    height: 60px;
    border-radius: 10px 0 0 10px;
    font-size: 14px;
    font-weight: 500;
    line-height: 20px;
    text-align: center;
    color: #abd1ff;
    background: rgba(0, 43, 137, 0.8);
    cursor: pointer;
  }
}
</style>
